import json
import time
from dataclasses import dataclass
from datetime import datetime, timedelta
import sys
import pathlib
import importlib

sys.path.insert(0, str(pathlib.Path(__file__).resolve().parent.parent))
ROOT = pathlib.Path(__file__).resolve().parents[0]  # project_root
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))
try:
    from utils import symbol_cache  # noqa: E402
except ModuleNotFoundError:
    SC_PATH = ROOT / "utils" / "symbol_cache.py"
    if not SC_PATH.exists():
        raise FileNotFoundError(f"找不到 {SC_PATH}")

    spec = importlib.util.spec_from_file_location("utils.symbol_cache", SC_PATH)
    symbol_cache: types.ModuleType = importlib.util.module_from_spec(spec)
    sys.modules[spec.name] = symbol_cache
    spec.loader.exec_module(symbol_cache)


import traderv2
import base_strategy


class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        self.cex_configs = cex_configs
        self.trader = trader
        self.symbol = symbol_cache.get_symbol()
        self.trader.log(f"load symbol from cache: {self.symbol}")
        self.last_report_time = datetime.now()
        self.long_target_price = 0.0
        self.short_target_price = 0.0
        self.leverage = 20
        self.position_amount = 0.0
        self.position_qty = 0.0
        self.balance = 0.0
        self.unrealized_pnl = 0.0
        self.orders = {}
        self.position_side = None
        self.max_open_qty = 0.0
        self.max_risk_limit = 0.0

    def start(self):
        self.trader.set_dual_side(1, False)
        if self.symbol:
            self.trader.set_leverage(1, self.symbol, 20)
            self.get_max_open_qty()
            self.cancel_all_orders()
        cmds = [
            {"account_id": 0, "cmd": {"Async": {"SignalCenterReport": {"CurrentLong": 0}}}},
            {"account_id": 0, "cmd": {"Async": {"SignalCenterReport": {"CurrentShort": 0}}}},
            {"account_id": 0, "cmd": {"Async": {"SignalCenterReport": {"AvailableLong": 0}}}},
            {"account_id": 0, "cmd": {"Async": {"SignalCenterReport": {"AvailableShort": 0}}}},
        ]
        return {"cmds": cmds}

    def get_max_open_qty(self):
        symbol = self.symbol.replace("_", "")
        path = "/v5/market/instruments-info"
        params = {"category": "linear", "symbol": symbol}
        result = self.trader.request(account_id=1, method="GET", path=path, query=params, auth=False)
        if "Ok" in result and result["Ok"] and "list" in result["Ok"]:
            lot_size_filter = result["Ok"]["list"][0]["lotSizeFilter"]
            self.max_open_qty = min(float(lot_size_filter["maxMktOrderQty"]), float(lot_size_filter["maxOrderQty"]))
            self.trader.log(f"获取最大持仓数量: {self.max_open_qty}")
        path = f"/v5/market/risk-limit"
        params = {"category": "linear", "symbol": symbol}
        result = self.trader.request(account_id=1, method="GET", path=path, query=params, auth=False)
        if "Ok" in result and result["Ok"] and "list" in result["Ok"]:
            risk_limit = result["Ok"]["list"][0]["riskLimitValue"]
            self.max_risk_limit = float(risk_limit)
            self.trader.log(f"获取最大风险限制: {self.max_risk_limit}")

    def on_stop(self):
        self.cancel_all_orders()

    def subscribes(self):
        """定时器"""
        if not self.symbol:
            return [{"account_id": 0, "sub": {"SubscribeWs": ["ControlSignal"]}}]
        subs = [
            {"account_id": 0, "sub": {"SubscribeWs": ["ControlSignal"]}},
            {
                "account_id": 1,
                "sub": {
                    "SubscribeWs": [
                        {
                            "Depth": {"symbols": [self.symbol], "levels": 20},
                        },
                    ]
                },
            },
            {
                "account_id": 1,
                "sub": {"SubscribeRest": {"update_interval": {"secs": 5, "nanos": 0}, "rest_type": "Balance"}},
            },
            {
                "account_id": 1,
                "sub": {"SubscribeRest": {"update_interval": {"secs": 5, "nanos": 0}, "rest_type": "Position"}},
            },
            {
                "account_id": 1,
                "sub": {
                    "SubscribeWs": [{"OrderAndFill": [self.symbol]}],
                },
            },
        ]
        # 输出订阅信息用于调试
        self.trader.log(f"📡 配置订阅: Order 数据={self.symbol}", level="INFO", color="blue")
        return subs

    def on_order_and_fill(self, account_id, order):
        if order["status"] == "Filled" or order["status"] == "PartiallyFilled":
            cid = order["cid"]
            if cid not in self.orders:
                self.trader.log(f"收到未知订单成交更新: {order}")
                return
            order_cmd = self.orders[cid]
            side = order_cmd["order"]["side"]
            self.trader.log("收到成交更新: {} {}".format(order["filled"], order["filled_avg_price"], side))

    def on_order_submitted(self, account_id, order_id_result, order):
        self.trader.log(f"收到订单提交回调: {order_id_result} {order}", level="INFO", color="blue")

    def on_balance(self, account_id, balances):
        for balance in balances:
            if balance["asset"] == "USDT":
                self.balance = float(balance["balance"])
                self.unrealized_pnl = float(balance["unrealized_pnl"])
                self.trader.log(f"收到余额更新: {self.balance} 未实现盈亏: {self.unrealized_pnl}")
                return {
                    "cmds": [
                        {
                            "account_id": 0,
                            "method": "SignalCenterReport",
                            "content": {
                                "Balance": self.balance,
                            },
                        },
                        {
                            "account_id": 0,
                            "method": "SignalCenterReport",
                            "content": {
                                "UnrealizedPnl": self.unrealized_pnl,
                            },
                        },
                    ]
                }

    def on_position(self, exchange, poistion):
        self.trader.log(f"收到仓位更新: {poistion}")
        if not poistion:
            self.position_amount = 0
            self.position_qty = 0
            return {
                "cmds": [
                    {
                        "account_id": 0,
                        "method": "SignalCenterReport",
                        "content": {"CurrentLong": 0},
                    },
                    {
                        "account_id": 0,
                        "method": "SignalCenterReport",
                        "content": {"CurrentShort": 0},
                    },
                ]
            }
        for p in poistion:
            if p["symbol"] == self.symbol:
                self.position_qty = float(p["amount"])
                self.position_amount = float(p["amount"]) * float(p["entry_price"])
                side = p["side"]
                self.position_side = side
                self.trader.log(f"收到仓位更新: {self.position_amount}")
                if side == "Long":
                    content = {"CurrentLong": self.position_amount}
                else:
                    content = {"CurrentShort": self.position_amount}
                return {
                    "cmds": [
                        {
                            "account_id": 0,
                            "method": "SignalCenterReport",
                            "content": content,
                        },
                    ]
                }

    def on_order(self, account_id, order):
        self.trader.log(f"订单更新: {order}")

    def on_depth(self, exchange, depth):
        bids = depth["bids"]
        asks = depth["asks"]
        bid_amount = 0
        ask_amount = 0
        bid_qty = 0
        ask_qty = 0
        for i in range(0, 10):
            bid_amount += bids[i][0] * bids[i][1]
            ask_amount += asks[i][0] * asks[i][1]
            bid_qty += bids[i][1]
            ask_qty += asks[i][1]
            if i == 9:
                self.long_target_price = asks[i][0]
                self.short_target_price = bids[i][0]
        max_long_qty = min(self.max_long(), ask_qty)
        max_short_qty = min(self.max_short(), bid_qty)
        max_long_amount = max_long_qty * self.long_target_price
        max_short_amount = max_short_qty * self.short_target_price
        if (datetime.now() - self.last_report_time).total_seconds() < 1:
            return
        self.last_report_time = datetime.now()
        self.trader.log(
            f"可开多: {max_long_amount} buy depth: {bid_amount} sell depth: {ask_amount} 可开空: {max_short_amount}",
            web=False,
        )
        return {
            "cmds": [
                {
                    "account_id": 0,
                    "method": "SignalCenterReport",
                    "content": {
                        "AvailableLong": max_long_amount,
                    },
                },
                {
                    "account_id": 0,
                    "method": "SignalCenterReport",
                    "content": {
                        "AvailableShort": max_short_amount,
                    },
                },
            ]
        }

    def max_long(self):
        long_amount = min(self.max_risk_limit, self.balance * self.leverage * 0.8)
        long_qty = long_amount / self.long_target_price
        return min(long_qty, self.max_open_qty)

    def max_short(self):
        short_amount = min(self.max_risk_limit, self.balance * self.leverage * 0.8)
        short_qty = short_amount / self.short_target_price
        return min(short_qty, self.max_open_qty)

    def on_signal(self, signal):
        if "ChangeSymbol" in signal:
            self.symbol = signal.split("_", 1)[1]
            self.trader.log(f"交易币种更换: {self.symbol}")
            symbol_cache.set_symbol(self.symbol)
            self.trader.graceful_shutdown()
        elif "OpenLong" in signal:
            self.trader.log("收到开仓信号")
            qty = self.balance * self.leverage / self.long_target_price * 0.8
            self.trader.log(f"开仓数量: {qty} {self.balance} {self.leverage} {self.long_target_price}")
            order_cmd = {
                "account_id": 1,
                "method": "PlaceOrder",
                "order": {
                    "symbol": self.symbol,
                    "order_type": "Limit",
                    "side": "Buy",
                    "price": self.long_target_price,
                    "amount": qty,
                    "time_in_force": "IOC",
                    "cid": self.trader.create_cid(self.cex_configs[1]["exchange"]),
                    "pos_side": None,
                },
                "params": {"is_dual_side": False, "margin_mode": "Cross", "leverage": 50},
            }
            self.orders[order_cmd["order"]["cid"]] = order_cmd
            self.trader.log(f"发送开仓命令: {order_cmd}")
            return {
                "cmds": [
                    order_cmd,
                ]
            }
        elif "OpenShort" in signal:
            self.trader.log("收到开仓信号")
            qty = self.balance * self.leverage / self.short_target_price * 0.8
            order_cmd = {
                "account_id": 1,
                "method": "PlaceOrder",
                "order": {
                    "symbol": self.symbol,
                    "order_type": "Limit",
                    "side": "Sell",
                    "price": self.short_target_price,
                    "amount": qty,
                    "time_in_force": "IOC",
                    "cid": self.trader.create_cid(self.cex_configs[1]["exchange"]),
                    "pos_side": None,
                },
                "params": {"is_dual_side": False, "margin_mode": "Cross", "leverage": 50},
            }
            self.orders[order_cmd["order"]["cid"]] = order_cmd
            self.trader.log(f"发送开仓命令: {order_cmd}")
            return {"cmds": [order_cmd]}
        elif "Close" in signal:
            side = "Buy" if self.position_side == "Short" else "Sell"
            pos_side = "Long" if self.position_side == "Short" else "Short"
            order_cmd = {
                "account_id": 1,
                "method": "PlaceOrder",
                "order": {
                    "symbol": self.symbol,
                    "order_type": "Market",
                    "side": side,
                    "price": self.long_target_price if self.position_qty < 0 else self.short_target_price,
                    "amount": self.position_qty,
                    "time_in_force": "IOC",
                    "cid": self.trader.create_cid(self.cex_configs[1]["exchange"]),
                    "pos_side": pos_side,
                },
                "params": {"is_dual_side": False, "margin_mode": "Cross", "leverage": 50},
            }
            self.orders[order_cmd["order"]["cid"]] = order_cmd
            self.trader.log(f"发送平仓命令: {order_cmd}")
            return {"cmds": [order_cmd]}
        else:
            self.trader.log("收到未知信号: {}".format(signal))

    def cancel_all_orders(self):
        """
        取消所有未完成订单

        遍历所有交易品种，取消所有挂起的订单
        """
        orders = self.get_open_orders(self.symbol)
        for order in orders:
            self.trader.log(f"取消订单: {order}", level="INFO", color="blue")
            res = self.trader.cancel_order(0, order["id"], self.symbol)
            self.trader.log(f"取消订单结果: {res}", level="INFO", color="blue")

    def get_open_orders(self, symbol: str) -> list:
        result = self.trader.get_open_orders(0, symbol)
        if "Ok" in result:
            orders = result["Ok"]
            if isinstance(orders, list) and len(orders) > 0:
                self.trader.log(f"获取挂单成功: {orders}", level="DEBUG")
                return orders
        else:
            self.trader.log(f"获取挂单失败: {result}", level="ERROR")
        return []
