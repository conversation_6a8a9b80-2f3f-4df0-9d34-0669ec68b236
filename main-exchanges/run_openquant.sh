#!/bin/bash

# OpenQuant 自动重启脚本
# 使用方法: ./run_openquant.sh [exchange_name]
# 例如: ./run_openquant.sh okx

set -e

# 默认配置
EXCHANGE_NAME=${1:-"default"}
OPENQUANT_BINARY="./open_quant"
CONFIG_FILE="config.toml"
LOG_DIR="logs"
MAX_RESTART_COUNT=10
RESTART_DELAY=5

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 创建日志目录
mkdir -p "$LOG_DIR"

# 检查openquant二进制文件
if [ ! -f "$OPENQUANT_BINARY" ]; then
    log_error "OpenQuant binary not found: $OPENQUANT_BINARY"
    exit 1
fi

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    log_error "Config file not found: $CONFIG_FILE"
    exit 1
fi

# 使openquant可执行
chmod +x "$OPENQUANT_BINARY"

# 清理函数
cleanup() {
    log "Received signal, cleaning up..."
    if [ ! -z "$OPENQUANT_PID" ] && kill -0 "$OPENQUANT_PID" 2>/dev/null; then
        log "Killing OpenQuant process (PID: $OPENQUANT_PID)"
        kill -TERM "$OPENQUANT_PID" 2>/dev/null || true
        sleep 2
        if kill -0 "$OPENQUANT_PID" 2>/dev/null; then
            log_warning "Force killing OpenQuant process"
            kill -KILL "$OPENQUANT_PID" 2>/dev/null || true
        fi
    fi
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主循环
restart_count=0
log "Starting OpenQuant auto-restart daemon for exchange: $EXCHANGE_NAME"
log "OpenQuant binary: $OPENQUANT_BINARY"
log "Config file: $CONFIG_FILE"
log "Log directory: $LOG_DIR"

while true; do
    if [ $restart_count -ge $MAX_RESTART_COUNT ]; then
        log_error "Maximum restart count ($MAX_RESTART_COUNT) reached. Exiting."
        exit 1
    fi

    if [ $restart_count -gt 0 ]; then
        log_warning "Restarting OpenQuant (attempt $restart_count/$MAX_RESTART_COUNT) in $RESTART_DELAY seconds..."
        sleep $RESTART_DELAY
    fi

    # 生成日志文件名
    LOG_FILE="$LOG_DIR/openquant_${EXCHANGE_NAME}_$(date '+%Y%m%d_%H%M%S').log"

    log "Starting OpenQuant process..."
    log "Log file: $LOG_FILE"

    # 启动OpenQuant进程
    "$OPENQUANT_BINARY" > "$LOG_FILE" 2>&1 &
    OPENQUANT_PID=$!

    log_success "OpenQuant started with PID: $OPENQUANT_PID"

    # 等待进程结束
    wait $OPENQUANT_PID
    EXIT_CODE=$?

    log_warning "OpenQuant process exited with code: $EXIT_CODE"

    # 检查退出原因
    if [ $EXIT_CODE -eq 0 ]; then
        log_success "OpenQuant exited normally"
        break
    elif [ $EXIT_CODE -eq 130 ]; then
        log "OpenQuant was interrupted (Ctrl+C)"
        break
    else
        log_error "OpenQuant crashed with exit code: $EXIT_CODE"
        restart_count=$((restart_count + 1))

        # 显示最后几行日志
        if [ -f "$LOG_FILE" ]; then
            log "Last 10 lines of log:"
            tail -n 10 "$LOG_FILE" | while read line; do
                echo "  $line"
            done
        fi
    fi
done

log "OpenQuant auto-restart daemon stopped"
