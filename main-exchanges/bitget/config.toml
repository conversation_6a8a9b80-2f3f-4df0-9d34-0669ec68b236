strategy_path = "./strategy.py"
trader_version = "V2"

[web]
is_production = true
secret_id = "T0CtwN0DT9lZjEkT"
secret_key = "f91cade43073ef03351789d6729f06c9"

[[exchanges]]
exchange = "SignalCenter"
is_colo = false
is_testnet = false
is_unified = false
key = "12345678"
passphrase = ""
rebate_rate = 0
secret = "12345678"
ws_host = "ws://**************:8181"
use_ws_api = true

[exchanges.params]
server_name = "bitget-1"
exchange_name = "bitget"

[[exchanges]]
exchange = "BitgetSwap"
is_colo = false
is_testnet = false
is_unified = false
key = "bg_00a7064c480820797a71a15a645faf59"
passphrase = "23s10l20sz"
rebate_rate = 0
secret = "0ad92a767fbef655582e25a067dde53a7e7fb5e0fd4c420c54165614b11e95ca"
use_ws_api = false
