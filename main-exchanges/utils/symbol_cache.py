# symbolcache.py
"""
轻量级单值缓存：
    set_symbol(symbol: str) -> None
    get_symbol()            -> str | None

数据持久化到与本文件同目录下的 `.symbol` 文本文件，
进程重启后自动恢复。
"""

from __future__ import annotations

import threading
from pathlib import Path

__all__ = ["set_symbol", "get_symbol"]

_LOCK = threading.RLock()
_CACHE_FILE: Path = Path(__file__).with_suffix(".symbol")
_SYMBOL: str | None = None


def _load() -> None:
    """模块导入时读取旧值。"""
    global _SYMBOL
    if _CACHE_FILE.exists():
        try:
            _SYMBOL = _CACHE_FILE.read_text("utf-8").strip() or None
        except Exception as exc:  # noqa: BLE001
            print(f"[symbolcache] WARNING: failed to load symbol — {exc}")


def _save() -> None:
    """原子写入：先写临时文件再替换，防止中途崩溃损坏数据。"""
    tmp = _CACHE_FILE.with_suffix(".tmp")
    tmp.write_text((_SYMBOL or "") + "\n", "utf-8")
    tmp.replace(_CACHE_FILE)  # 原子 rename


def set_symbol(symbol: str) -> None:
    """
    设置并持久化 symbol。

    >>> set_symbol("BTCUSDT")
    """
    global _SYMBOL
    with _LOCK:
        _SYMBOL = symbol
        _save()


def get_symbol() -> str | None:
    """
    读取缓存的 symbol；如果还没有设置过返回 None。

    >>> get_symbol()
    'BTCUSDT'
    """
    with _LOCK:
        return _SYMBOL


# 模块首次 import 时自动恢复旧值
_load()
