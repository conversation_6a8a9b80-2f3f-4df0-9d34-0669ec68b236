#!/bin/bash

# 自动部署脚本
# 部署各个交易所的配置文件和策略文件到远程服务器
# 使用方法: ./deploy.sh [exchange1,exchange2,...] 或 ./deploy.sh all

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 交易所配置
declare -A EXCHANGE_HOSTS
EXCHANGE_HOSTS["okx"]="okx-trend"
EXCHANGE_HOSTS["bitget"]="bitget-trend"
EXCHANGE_HOSTS["bybit"]="bybit-trend"

# 默认部署的交易所
DEFAULT_EXCHANGES=("okx" "bitget" "bybit")

# 需要部署的文件
FILES_TO_DEPLOY=("config.toml" "strategy.py")
COMMON_FILES_TO_DEPLOY=("start_openquant.sh")

# 远程目录
REMOTE_DIR="~/"

# 显示帮助信息
show_help() {
    echo "Usage: $0 [OPTIONS] [EXCHANGES]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -n, --dry-run  Show what would be deployed without actually doing it"
    echo "  -f, --force    Force deployment even if files are the same"
    echo ""
    echo "Examples:"
    echo "  $0                    # Deploy to all exchanges"
    echo "  $0 all                # Deploy to all exchanges"
    echo "  $0 okx                # Deploy to okx only"
    echo "  $0 okx,bitget         # Deploy to okx and bitget"
    echo "  $0 --dry-run okx      # Show what would be deployed to okx"
    echo ""
    echo "Available exchanges: ${!EXCHANGE_HOSTS[@]}"
}

# 解析命令行参数
DRY_RUN=false
FORCE=false
EXCHANGES_TO_DEPLOY=()

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -n|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        all)
            EXCHANGES_TO_DEPLOY=("${DEFAULT_EXCHANGES[@]}")
            shift
            ;;
        *)
            # 解析交易所列表
            IFS=',' read -ra ADDR <<< "$1"
            for exchange in "${ADDR[@]}"; do
                if [[ " ${!EXCHANGE_HOSTS[@]} " =~ " ${exchange} " ]]; then
                    EXCHANGES_TO_DEPLOY+=("$exchange")
                else
                    log_error "Unknown exchange: $exchange"
                    log "Available exchanges: ${!EXCHANGE_HOSTS[@]}"
                    exit 1
                fi
            done
            shift
            ;;
    esac
done

# 如果没有指定交易所，默认部署所有
if [ ${#EXCHANGES_TO_DEPLOY[@]} -eq 0 ]; then
    EXCHANGES_TO_DEPLOY=("${DEFAULT_EXCHANGES[@]}")
fi

# 检查必要的工具
check_dependencies() {
    local missing_tools=()

    if ! command -v scp &> /dev/null; then
        missing_tools+=("scp")
    fi

    if ! command -v ssh &> /dev/null; then
        missing_tools+=("ssh")
    fi

    if [ ${#missing_tools[@]} -gt 0 ]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log "Please install the missing tools and try again"
        exit 1
    fi
}

# 检查文件是否存在
check_files() {
    local exchange=$1
    local missing_files=()

    # 检查交易所特定文件
    for file in "${FILES_TO_DEPLOY[@]}"; do
        local file_path="$exchange/$file"
        if [ ! -f "$file_path" ]; then
            missing_files+=("$file_path")
        fi
    done

    # 检查通用文件
    for file in "${COMMON_FILES_TO_DEPLOY[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done

    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "Missing files for $exchange: ${missing_files[*]}"
        return 1
    fi

    return 0
}

# 部署单个交易所
deploy_exchange() {
    local exchange=$1
    local host=${EXCHANGE_HOSTS[$exchange]}

    log "Deploying $exchange to $host..."

    # 检查文件
    if ! check_files "$exchange"; then
        return 1
    fi

    # 检查远程主机连接
    if [ "$DRY_RUN" = false ]; then
        if ! timeout 5 ssh -o ConnectTimeout=3 -o BatchMode=yes "$host" exit 2>/dev/null; then
            log_error "Cannot connect to $host. Please check SSH configuration."
            return 1
        fi
    fi

    # 部署交易所特定文件
    for file in "${FILES_TO_DEPLOY[@]}"; do
        local local_file="$exchange/$file"
        local remote_file="$host:$REMOTE_DIR$file"

        if [ "$DRY_RUN" = true ]; then
            log "Would copy: $local_file -> $remote_file"
        else
            log "Copying: $local_file -> $remote_file"
            if scp -o ConnectTimeout=10 "$local_file" "$remote_file"; then
                log_success "Successfully copied $file"
            else
                log_error "Failed to copy $file"
                return 1
            fi
        fi
    done

    # 部署通用文件
    for file in "${COMMON_FILES_TO_DEPLOY[@]}"; do
        local local_file="$file"
        local remote_file="$host:$REMOTE_DIR$file"

        if [ "$DRY_RUN" = true ]; then
            log "Would copy: $local_file -> $remote_file"
        else
            log "Copying: $local_file -> $remote_file"
            if scp -o ConnectTimeout=10 "$local_file" "$remote_file"; then
                # 使脚本可执行
                if [[ "$file" == *.sh ]]; then
                    ssh -o ConnectTimeout=5 "$host" "chmod +x $REMOTE_DIR$file" 2>/dev/null || true
                fi
                log_success "Successfully copied $file"
            else
                log_error "Failed to copy $file"
                return 1
            fi
        fi
    done

    # 重启OpenQuant
    if [ "$DRY_RUN" = true ]; then
        log "Would restart OpenQuant on $host"
    else
        log "Restarting OpenQuant on $host..."

        # 停止现有进程（不等待确认，避免卡住）
        log "Stopping existing processes on $host..."
        ssh -o ConnectTimeout=5 "$host" "pkill -f 'start_openquant.sh auto' || true; pkill -f openquant || true" 2>/dev/null || true

        # 启动自动重启模式（使用简化的方法避免卡住）
        log "Starting auto-restart mode on $host..."

        # 使用 timeout 和简化的启动方式
        if timeout 10 ssh -o ConnectTimeout=5 "$host" \
            "cd ~ && nohup ./start_openquant.sh auto > auto_restart.log 2>&1 & echo \$! > auto_restart.pid" 2>/dev/null; then
            log_success "Auto-restart mode started on $host"

            # 简化的状态检查，避免复杂的管道操作
            sleep 2
            if ssh -o ConnectTimeout=5 "$host" "kill -0 \$(cat auto_restart.pid 2>/dev/null) 2>/dev/null"; then
                log_success "Auto-restart daemon is running on $host"
            else
                log_warning "Auto-restart daemon may not be running on $host, but this is sometimes normal"
            fi
        else
            log_error "Failed to start auto-restart mode on $host"
            return 1
        fi
    fi

    return 0
}

# 主函数
main() {
    log "Starting deployment process..."

    if [ "$DRY_RUN" = true ]; then
        log_warning "DRY RUN MODE - No actual changes will be made"
    fi

    log "Exchanges to deploy: ${EXCHANGES_TO_DEPLOY[*]}"

    # 检查依赖
    check_dependencies

    # 部署统计
    local success_count=0
    local total_count=${#EXCHANGES_TO_DEPLOY[@]}
    local failed_exchanges=()

    # 部署每个交易所
    for exchange in "${EXCHANGES_TO_DEPLOY[@]}"; do
        echo ""
        log "=" "Processing $exchange" "="

        if deploy_exchange "$exchange"; then
            success_count=$((success_count + 1))
            log_success "Deployment completed for $exchange"
        else
            failed_exchanges+=("$exchange")
            log_error "Deployment failed for $exchange"
        fi
    done

    # 显示部署结果
    echo ""
    log "=" "Deployment Summary" "="
    log "Total exchanges: $total_count"
    log_success "Successful: $success_count"

    if [ ${#failed_exchanges[@]} -gt 0 ]; then
        log_error "Failed: ${#failed_exchanges[@]} (${failed_exchanges[*]})"
        exit 1
    else
        log_success "All deployments completed successfully!"
    fi
}

# 运行主函数
main "$@"
