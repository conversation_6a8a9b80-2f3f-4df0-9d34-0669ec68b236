import json
import time

import traderv2 # type: ignore
import base_strategy
import datetime

class Strategy(base_strategy.BaseStrategy):
    def __init__(self, cex_configs, dex_configs, config, trader: traderv2.TraderV2):
        self.trader = trader
        self.send_order = False
        self.status = 0 # 0 未开始 1 开d多仓 2 平空仓 3 已完成
        self.symbol = ""
        pass

    def name(self):
        return "Backtest"

    def start(self):
        pass

    def subscribes(self):
        subs = [
             {
                "account_id": 0,
                "sub": {
                    "SubscribeWs": [
                        "ControlSingal"
                    ]
                }
            },
            {
                "account_id": 0,
                "sub": {
                    "SubscribeTimer": {
                        "update_interval": {"secs": 1, "nanos": 0},
                        "name": "test_position_update",
                        "initial_delay": {"secs": 1, "nanos": 0}
                    }
                }
            }
        ]
        return subs

    def on_bbo(self, exchange, bbo):
        self.trader.log(f"bbo timestmap {bbo['timestamp']}")
        dt_utc = datetime.datetime.fromtimestamp(bbo['timestamp'] / 1000000)
        self.trader.log(f"收到BBO数据: {dt_utc.strftime("%Y-%m-%d %H:%M:%S.%f")} {exchange} {bbo}", level="INFO", color="blue")
        if self.send_order == True:
            return
        self.send_order = True
        return {
            'cmds': [
                {
                    'account_id': 0,
                    'method': 'SignalCenterReport',
                    'content': {
                        "AvaliableOpen": 10000
                    }
                },
                {
                    'account_id': 0,
                    'method': 'SignalCenterReport',
                    'content': {
                        "StartClose": 10000
                    }
                },
                {
                    'account_id': 0,
                    'method': 'SignalCenterReport',
                    'content': {
                        "StartOpen": 10000
                    }
                },
                {
                    'account_id': 0,
                    'method': 'SignalCenterReport',
                    'content': {
                        "CurrentPosition": 10000
                    }
                }
            ]
        }
        # self.trader.log(f"收到BBO数据: {bbo}", level="INFO", color="blue")

    def on_depth(self, exchange, depth):
        pass

    def on_order_submitted(self, account_id, order_id_result, order):
        pass

    def on_order_and_fill(self, account_id, order):
        pass

    def on_order(self, account_id, order):
        pass

    def on_signal(self, signal):
        self.trader.log(f"收到信号: {signal}", level="INFO", color="blue")
        if signal == "Open":
            self.status = 1
        elif signal == "Close":
            self.status = 2
        elif signal == "Stop":
            self.status = 0
        elif "ChangeSymbol_" in signal:
            self.symbol = signal.split("_")[1]
            self.trader.log(f"交易币种更换: {self.symbol}")

    def on_timer_subscribe(self, timer_name):
        if self.status == 1:
            return {
                'cmds': [
                    {
                        'account_id': 0,
                        'method': 'SignalCenterReport',
                        'content': {
                            "AvailableLong": 10000
                        }
                    },
                    {
                        'account_id': 0,
                        'method': 'SignalCenterReport',
                        'content': {
                            "CurrentLong": 10000
                        }
                    },
                ]
            }
