#!/bin/bash

# 快速部署脚本 - 简化版本，避免卡住
# 专注于快速部署和启动，减少状态检查

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 交易所配置
declare -A EXCHANGE_HOSTS
EXCHANGE_HOSTS["okx"]="okx-trend"
EXCHANGE_HOSTS["bitget"]="bitget-trend"
EXCHANGE_HOSTS["bybit"]="bybit-trend"

# 默认部署的交易所
DEFAULT_EXCHANGES=("okx" "bitget" "bybit")

# 需要部署的文件
FILES_TO_DEPLOY=("config.toml" "strategy.py")
COMMON_FILES_TO_DEPLOY=("start_openquant.sh" "daemon_start.sh")

# 远程目录
REMOTE_DIR="~/"

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Usage: $0 [EXCHANGES]"
    echo ""
    echo "快速部署脚本 - 简化版本"
    echo ""
    echo "Examples:"
    echo "  $0                    # Deploy to all exchanges"
    echo "  $0 all                # Deploy to all exchanges"
    echo "  $0 okx                # Deploy to okx only"
    echo "  $0 okx,bitget         # Deploy to okx and bitget"
    echo ""
    echo "Available exchanges: ${!EXCHANGE_HOSTS[@]}"
}

# 解析命令行参数
EXCHANGES_TO_DEPLOY=()

if [ $# -eq 0 ] || [ "$1" = "all" ]; then
    EXCHANGES_TO_DEPLOY=("${DEFAULT_EXCHANGES[@]}")
elif [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
else
    # 解析交易所列表
    IFS=',' read -ra ADDR <<< "$1"
    for exchange in "${ADDR[@]}"; do
        if [[ " ${!EXCHANGE_HOSTS[@]} " =~ " ${exchange} " ]]; then
            EXCHANGES_TO_DEPLOY+=("$exchange")
        else
            log_error "Unknown exchange: $exchange"
            log "Available exchanges: ${!EXCHANGE_HOSTS[@]}"
            exit 1
        fi
    done
fi

# 检查文件是否存在
check_files() {
    local exchange=$1
    local missing_files=()

    # 检查交易所特定文件
    for file in "${FILES_TO_DEPLOY[@]}"; do
        local file_path="$exchange/$file"
        if [ ! -f "$file_path" ]; then
            missing_files+=("$file_path")
        fi
    done

    # 检查通用文件
    for file in "${COMMON_FILES_TO_DEPLOY[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done

    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "Missing files for $exchange: ${missing_files[*]}"
        return 1
    fi

    return 0
}

# 快速部署单个交易所
quick_deploy_exchange() {
    local exchange=$1
    local host=${EXCHANGE_HOSTS[$exchange]}

    log "Quick deploying $exchange to $host..."

    # 检查文件
    if ! check_files "$exchange"; then
        return 1
    fi

    # 检查远程主机连接（快速检查）
    if ! timeout 5 ssh -o ConnectTimeout=3 -o BatchMode=yes "$host" exit 2>/dev/null; then
        log_error "Cannot connect to $host"
        return 1
    fi

    # 停止现有进程（不等待确认）
    log "Stopping existing processes on $host..."
    ssh -o ConnectTimeout=5 "$host" "pkill -f 'start_openquant.sh auto' || true; pkill -f openquant || true" 2>/dev/null || true

    # 部署交易所特定文件
    for file in "${FILES_TO_DEPLOY[@]}"; do
        local local_file="$exchange/$file"
        local remote_file="$host:$REMOTE_DIR$file"

        log "Copying: $local_file -> $remote_file"
        if ! scp -o ConnectTimeout=10 "$local_file" "$remote_file"; then
            log_error "Failed to copy $file"
            return 1
        fi
    done

    # 部署通用文件
    for file in "${COMMON_FILES_TO_DEPLOY[@]}"; do
        local local_file="$file"
        local remote_file="$host:$REMOTE_DIR$file"

        log "Copying: $local_file -> $remote_file"
        if ! scp -o ConnectTimeout=10 "$local_file" "$remote_file"; then
            log_error "Failed to copy $file"
            return 1
        fi

        # 使脚本可执行
        if [[ "$file" == *.sh ]]; then
            ssh -o ConnectTimeout=5 "$host" "chmod +x $REMOTE_DIR$file" 2>/dev/null || true
        fi
    done

    log_success "Files deployed to $exchange"

    # 启动自动重启模式（使用setsid完全分离进程）
    log "Starting auto-restart mode on $host..."

    # 方法1: 使用setsid完全分离进程
    if timeout 5 ssh -o ConnectTimeout=3 "$host" \
        "cd ~ && setsid ./start_openquant.sh auto < /dev/null > auto_restart.log 2>&1 & echo \$! > auto_restart.pid" 2>/dev/null; then
        log_success "Auto-restart mode started on $exchange"
    else
        # 方法2: 如果setsid不可用，使用传统方法但立即断开
        log "Trying alternative method..."
        ssh -o ConnectTimeout=3 "$host" \
            "cd ~ && (./start_openquant.sh auto > auto_restart.log 2>&1 < /dev/null & echo \$! > auto_restart.pid) && exit" 2>/dev/null &
        sleep 2  # 给SSH时间启动进程
        log_success "Auto-restart mode started on $exchange (alternative method)"
    fi

    return 0
}

# 主函数
main() {
    log "Starting quick deployment..."
    log "Exchanges to deploy: ${EXCHANGES_TO_DEPLOY[*]}"

    # 部署统计
    local success_count=0
    local total_count=${#EXCHANGES_TO_DEPLOY[@]}
    local failed_exchanges=()

    # 部署每个交易所
    for exchange in "${EXCHANGES_TO_DEPLOY[@]}"; do
        echo ""
        log "Processing $exchange..."

        if quick_deploy_exchange "$exchange"; then
            success_count=$((success_count + 1))
            log_success "Quick deployment completed for $exchange"
        else
            failed_exchanges+=("$exchange")
            log_error "Quick deployment failed for $exchange"
        fi
    done

    # 显示部署结果
    echo ""
    log "Quick Deployment Summary"
    log "Total exchanges: $total_count"
    log_success "Successful: $success_count"

    if [ ${#failed_exchanges[@]} -gt 0 ]; then
        log_error "Failed: ${#failed_exchanges[@]} (${failed_exchanges[*]})"
        echo ""
        log "To check status later, run: ./check_status.sh status"
        exit 1
    else
        log_success "All quick deployments completed!"
        echo ""
        log "Services are starting in the background."
        log "To check status, run: ./check_status.sh status"
        log "To view logs: ssh <host> 'tail -f openquant.log'"
    fi
}

# 运行主函数
main "$@"
