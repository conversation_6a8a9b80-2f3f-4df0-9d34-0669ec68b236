#!/bin/bash

# 检查所有交易所OpenQuant运行状态的脚本

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 交易所配置
declare -A EXCHANGE_HOSTS
EXCHANGE_HOSTS["okx"]="okx-trend"
EXCHANGE_HOSTS["bitget"]="bitget-trend"
EXCHANGE_HOSTS["bybit"]="bybit-trend"

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 检查单个交易所状态
check_exchange_status() {
    local exchange=$1
    local host=${EXCHANGE_HOSTS[$exchange]}

    echo ""
    echo "=================================="
    echo "检查 $exchange ($host) 状态"
    echo "=================================="

    # 检查SSH连接
    if ! ssh -o ConnectTimeout=5 -o BatchMode=yes "$host" exit 2>/dev/null; then
        log_error "无法连接到 $host"
        return 1
    fi

    log_success "SSH连接正常"

    # 检查自动重启守护进程状态
    if ssh "$host" "kill -0 \$(cat auto_restart.pid 2>/dev/null) 2>/dev/null"; then
        local auto_pid=$(ssh "$host" "cat auto_restart.pid 2>/dev/null")
        log_success "自动重启守护进程运行中 (PID: $auto_pid)"
    else
        log_warning "自动重启守护进程未运行"
    fi

    # 检查OpenQuant状态（添加超时）
    log "检查OpenQuant状态..."
    local status_output=$(timeout 10 ssh -o ConnectTimeout=5 "$host" "cd ~ && ./start_openquant.sh status" 2>/dev/null)
    local status_exit_code=$?

    if [ $status_exit_code -eq 124 ]; then
        log_warning "OpenQuant状态检查超时"
    elif [ $status_exit_code -eq 0 ] && echo "$status_output" | grep -q "is running"; then
        local openquant_pid=$(echo "$status_output" | grep -o "PID: [0-9]*" | cut -d' ' -f2)
        log_success "OpenQuant运行中 (PID: $openquant_pid)"
    else
        log_error "OpenQuant未运行"
    fi

    # 显示最近的日志
    echo ""
    echo "最近的OpenQuant日志:"
    echo "--------------------"
    ssh "$host" "tail -n 5 openquant.log 2>/dev/null || echo '无日志文件'"

    echo ""
    echo "最近的自动重启日志:"
    echo "--------------------"
    ssh "$host" "tail -n 5 auto_restart.log 2>/dev/null || echo '无自动重启日志'"

    return 0
}

# 停止所有交易所的OpenQuant
stop_all() {
    log "停止所有交易所的OpenQuant..."

    for exchange in "${!EXCHANGE_HOSTS[@]}"; do
        local host=${EXCHANGE_HOSTS[$exchange]}
        echo ""
        log "停止 $exchange ($host)..."

        # 停止自动重启守护进程
        ssh "$host" "pkill -f 'start_openquant.sh auto' || true" 2>/dev/null || true

        # 停止OpenQuant
        ssh "$host" "cd ~ && ./start_openquant.sh stop" 2>/dev/null || true

        log_success "$exchange 已停止"
    done
}

# 启动所有交易所的OpenQuant自动重启模式
start_all() {
    log "启动所有交易所的OpenQuant自动重启模式..."

    for exchange in "${!EXCHANGE_HOSTS[@]}"; do
        local host=${EXCHANGE_HOSTS[$exchange]}
        echo ""
        log "启动 $exchange ($host)..."

        # 启动自动重启模式
        if ssh "$host" "cd ~ && nohup ./start_openquant.sh auto > auto_restart.log 2>&1 & echo \$! > auto_restart.pid" 2>/dev/null; then
            log_success "$exchange 自动重启模式已启动"
        else
            log_error "$exchange 启动失败"
        fi
    done

    # 等待一下再检查状态
    log "等待3秒后检查状态..."
    sleep 3

    for exchange in "${!EXCHANGE_HOSTS[@]}"; do
        local host=${EXCHANGE_HOSTS[$exchange]}
        if ssh "$host" "kill -0 \$(cat auto_restart.pid 2>/dev/null) 2>/dev/null"; then
            log_success "$exchange 自动重启守护进程运行正常"
        else
            log_error "$exchange 自动重启守护进程启动失败"
        fi
    done
}

# 重启所有交易所的OpenQuant
restart_all() {
    log "重启所有交易所的OpenQuant..."
    stop_all
    sleep 3
    start_all
}

# 显示帮助
show_help() {
    echo "Usage: $0 [COMMAND] [EXCHANGE]"
    echo ""
    echo "Commands:"
    echo "  status [exchange]    显示状态 (默认显示所有)"
    echo "  stop [exchange]      停止OpenQuant (默认停止所有)"
    echo "  start [exchange]     启动自动重启模式 (默认启动所有)"
    echo "  restart [exchange]   重启OpenQuant (默认重启所有)"
    echo "  help                 显示帮助信息"
    echo ""
    echo "Available exchanges: ${!EXCHANGE_HOSTS[@]}"
    echo ""
    echo "Examples:"
    echo "  $0 status           # 检查所有交易所状态"
    echo "  $0 status okx       # 检查okx状态"
    echo "  $0 restart okx      # 重启okx"
    echo "  $0 stop             # 停止所有"
}

# 主函数
main() {
    local command=${1:-status}
    local exchange=$2

    case $command in
        status)
            if [ -n "$exchange" ]; then
                if [[ " ${!EXCHANGE_HOSTS[@]} " =~ " ${exchange} " ]]; then
                    check_exchange_status "$exchange"
                else
                    log_error "未知交易所: $exchange"
                    echo "可用交易所: ${!EXCHANGE_HOSTS[@]}"
                    exit 1
                fi
            else
                log "检查所有交易所状态..."
                for exchange in "${!EXCHANGE_HOSTS[@]}"; do
                    check_exchange_status "$exchange"
                done
            fi
            ;;
        stop)
            if [ -n "$exchange" ]; then
                if [[ " ${!EXCHANGE_HOSTS[@]} " =~ " ${exchange} " ]]; then
                    local host=${EXCHANGE_HOSTS[$exchange]}
                    log "停止 $exchange ($host)..."
                    ssh "$host" "pkill -f 'start_openquant.sh auto' || true" 2>/dev/null || true
                    ssh "$host" "cd ~ && ./start_openquant.sh stop" 2>/dev/null || true
                    log_success "$exchange 已停止"
                else
                    log_error "未知交易所: $exchange"
                    exit 1
                fi
            else
                stop_all
            fi
            ;;
        start)
            if [ -n "$exchange" ]; then
                if [[ " ${!EXCHANGE_HOSTS[@]} " =~ " ${exchange} " ]]; then
                    local host=${EXCHANGE_HOSTS[$exchange]}
                    log "启动 $exchange ($host) 自动重启模式..."
                    if ssh "$host" "cd ~ && nohup ./start_openquant.sh auto > auto_restart.log 2>&1 & echo \$! > auto_restart.pid" 2>/dev/null; then
                        log_success "$exchange 自动重启模式已启动"
                    else
                        log_error "$exchange 启动失败"
                    fi
                else
                    log_error "未知交易所: $exchange"
                    exit 1
                fi
            else
                start_all
            fi
            ;;
        restart)
            if [ -n "$exchange" ]; then
                if [[ " ${!EXCHANGE_HOSTS[@]} " =~ " ${exchange} " ]]; then
                    local host=${EXCHANGE_HOSTS[$exchange]}
                    log "重启 $exchange ($host)..."
                    ssh "$host" "pkill -f 'start_openquant.sh auto' || true" 2>/dev/null || true
                    ssh "$host" "cd ~ && ./start_openquant.sh stop" 2>/dev/null || true
                    sleep 2
                    if ssh "$host" "cd ~ && nohup ./start_openquant.sh auto > auto_restart.log 2>&1 & echo \$! > auto_restart.pid" 2>/dev/null; then
                        log_success "$exchange 重启完成"
                    else
                        log_error "$exchange 重启失败"
                    fi
                else
                    log_error "未知交易所: $exchange"
                    exit 1
                fi
            else
                restart_all
            fi
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
