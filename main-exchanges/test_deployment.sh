#!/bin/bash

# 测试部署脚本
# 用于验证部署环境是否正确配置

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 交易所配置
declare -A EXCHANGE_HOSTS
EXCHANGE_HOSTS["okx"]="okx-trend"
EXCHANGE_HOSTS["bitget"]="bitget-trend"
EXCHANGE_HOSTS["bybit"]="bybit-trend"

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

# 检查本地文件
check_local_files() {
    log "检查本地文件..."
    
    local missing_files=()
    
    # 检查脚本文件
    for script in "deploy.sh" "start_openquant.sh" "check_status.sh"; do
        if [ ! -f "$script" ]; then
            missing_files+=("$script")
        elif [ ! -x "$script" ]; then
            log_warning "$script 不可执行，正在修复..."
            chmod +x "$script"
        fi
    done
    
    # 检查交易所文件
    for exchange in "${!EXCHANGE_HOSTS[@]}"; do
        for file in "config.toml" "strategy.py"; do
            local file_path="$exchange/$file"
            if [ ! -f "$file_path" ]; then
                missing_files+=("$file_path")
            fi
        done
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少文件: ${missing_files[*]}"
        return 1
    fi
    
    log_success "本地文件检查通过"
    return 0
}

# 检查SSH连接
check_ssh_connections() {
    log "检查SSH连接..."
    
    local failed_hosts=()
    
    for exchange in "${!EXCHANGE_HOSTS[@]}"; do
        local host=${EXCHANGE_HOSTS[$exchange]}
        log "测试连接到 $exchange ($host)..."
        
        if ssh -o ConnectTimeout=5 -o BatchMode=yes "$host" exit 2>/dev/null; then
            log_success "$exchange 连接正常"
        else
            log_error "$exchange 连接失败"
            failed_hosts+=("$exchange")
        fi
    done
    
    if [ ${#failed_hosts[@]} -gt 0 ]; then
        log_error "SSH连接失败的主机: ${failed_hosts[*]}"
        return 1
    fi
    
    log_success "所有SSH连接正常"
    return 0
}

# 检查远程环境
check_remote_environment() {
    log "检查远程环境..."
    
    for exchange in "${!EXCHANGE_HOSTS[@]}"; do
        local host=${EXCHANGE_HOSTS[$exchange]}
        log "检查 $exchange ($host) 远程环境..."
        
        # 检查openquant二进制文件
        if ssh "$host" "test -f ./openquant" 2>/dev/null; then
            log_success "$exchange: openquant 二进制文件存在"
        else
            log_warning "$exchange: openquant 二进制文件不存在"
        fi
        
        # 检查目录权限
        if ssh "$host" "test -w ." 2>/dev/null; then
            log_success "$exchange: 目录可写"
        else
            log_error "$exchange: 目录不可写"
        fi
        
        # 检查磁盘空间
        local disk_usage=$(ssh "$host" "df -h . | tail -1 | awk '{print \$5}' | sed 's/%//'" 2>/dev/null)
        if [ -n "$disk_usage" ] && [ "$disk_usage" -lt 90 ]; then
            log_success "$exchange: 磁盘空间充足 (已用 ${disk_usage}%)"
        else
            log_warning "$exchange: 磁盘空间可能不足 (已用 ${disk_usage}%)"
        fi
    done
}

# 测试干运行部署
test_dry_run() {
    log "测试干运行部署..."
    
    if ./deploy.sh --dry-run all; then
        log_success "干运行测试通过"
        return 0
    else
        log_error "干运行测试失败"
        return 1
    fi
}

# 显示部署建议
show_deployment_advice() {
    echo ""
    echo "=================================="
    echo "部署建议"
    echo "=================================="
    echo ""
    echo "1. 如果所有检查都通过，可以执行实际部署："
    echo "   ./deploy.sh all"
    echo ""
    echo "2. 部署后检查状态："
    echo "   ./check_status.sh status"
    echo ""
    echo "3. 如果需要停止所有服务："
    echo "   ./check_status.sh stop"
    echo ""
    echo "4. 如果需要重启所有服务："
    echo "   ./check_status.sh restart"
    echo ""
    echo "5. 查看特定交易所日志："
    echo "   ssh okx-trend 'tail -f openquant.log'"
    echo "   ssh okx-trend 'tail -f auto_restart.log'"
    echo ""
}

# 主函数
main() {
    echo "=================================="
    echo "OpenQuant 部署环境测试"
    echo "=================================="
    echo ""
    
    local all_passed=true
    
    # 执行所有检查
    if ! check_local_files; then
        all_passed=false
    fi
    
    echo ""
    
    if ! check_ssh_connections; then
        all_passed=false
    fi
    
    echo ""
    
    check_remote_environment
    
    echo ""
    
    if ! test_dry_run; then
        all_passed=false
    fi
    
    echo ""
    echo "=================================="
    echo "测试结果"
    echo "=================================="
    
    if [ "$all_passed" = true ]; then
        log_success "所有测试通过！环境配置正确，可以进行部署。"
        show_deployment_advice
        exit 0
    else
        log_error "部分测试失败，请修复问题后重试。"
        exit 1
    fi
}

# 显示帮助
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help     显示帮助信息"
    echo ""
    echo "这个脚本会检查："
    echo "  - 本地文件完整性"
    echo "  - SSH连接配置"
    echo "  - 远程环境状态"
    echo "  - 部署脚本干运行"
    echo ""
    echo "在实际部署前运行此脚本以确保环境配置正确。"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help|help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
