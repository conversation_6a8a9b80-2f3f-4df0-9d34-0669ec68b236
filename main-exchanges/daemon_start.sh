#!/bin/bash

# 守护进程启动脚本
# 这个脚本专门用于在远程服务器上启动OpenQuant守护进程
# 它会立即返回，不会阻塞SSH连接

set -e

# 配置
OPENQUANT_BINARY="./openquant"
CONFIG_FILE="config.toml"
PID_FILE="openquant.pid"
LOG_FILE="openquant.log"
DAEMON_PID_FILE="auto_restart.pid"
DAEMON_LOG_FILE="auto_restart.log"
MAX_RESTART_COUNT=10
RESTART_DELAY=3

# 检查是否已经在运行
if [ -f "$DAEMON_PID_FILE" ]; then
    daemon_pid=$(cat "$DAEMON_PID_FILE" 2>/dev/null)
    if kill -0 "$daemon_pid" 2>/dev/null; then
        echo "Auto-restart daemon already running (PID: $daemon_pid)"
        exit 0
    else
        rm -f "$DAEMON_PID_FILE"
    fi
fi

# 停止现有的OpenQuant进程
if [ -f "$PID_FILE" ]; then
    old_pid=$(cat "$PID_FILE" 2>/dev/null)
    if kill -0 "$old_pid" 2>/dev/null; then
        echo "Stopping existing OpenQuant (PID: $old_pid)"
        kill -TERM "$old_pid" 2>/dev/null || true
        sleep 2
        if kill -0 "$old_pid" 2>/dev/null; then
            kill -KILL "$old_pid" 2>/dev/null || true
        fi
    fi
    rm -f "$PID_FILE"
fi

# 创建守护进程脚本
cat > /tmp/openquant_daemon.sh << 'EOF'
#!/bin/bash

OPENQUANT_BINARY="./openquant"
PID_FILE="openquant.pid"
LOG_FILE="openquant.log"
MAX_RESTART_COUNT=10
RESTART_DELAY=3

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> auto_restart.log
}

check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

start_openquant() {
    if [ ! -f "$OPENQUANT_BINARY" ]; then
        log "ERROR: OpenQuant binary not found"
        return 1
    fi
    
    chmod +x "$OPENQUANT_BINARY"
    
    log "Starting OpenQuant..."
    nohup "$OPENQUANT_BINARY" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    sleep 2
    if kill -0 "$pid" 2>/dev/null; then
        log "OpenQuant started successfully (PID: $pid)"
        return 0
    else
        log "OpenQuant failed to start"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 主循环
restart_count=0
log "Starting OpenQuant auto-restart daemon"

while true; do
    if [ $restart_count -ge $MAX_RESTART_COUNT ]; then
        log "Maximum restart count reached. Exiting."
        exit 1
    fi
    
    if ! check_running; then
        if [ $restart_count -gt 0 ]; then
            log "Waiting $RESTART_DELAY seconds before restart..."
            sleep $RESTART_DELAY
        fi
        
        log "Starting OpenQuant (attempt $((restart_count + 1)))..."
        if start_openquant; then
            restart_count=0
        else
            restart_count=$((restart_count + 1))
            log "Start failed, will retry..."
        fi
    fi
    
    sleep 5
done
EOF

# 使守护进程脚本可执行
chmod +x /tmp/openquant_daemon.sh

# 启动守护进程（完全分离）
echo "Starting OpenQuant auto-restart daemon..."
cd ~
nohup /tmp/openquant_daemon.sh > /dev/null 2>&1 &
daemon_pid=$!
echo $daemon_pid > "$DAEMON_PID_FILE"

# 等待一下确保启动
sleep 1

if kill -0 "$daemon_pid" 2>/dev/null; then
    echo "Auto-restart daemon started successfully (PID: $daemon_pid)"
    echo "Logs: $DAEMON_LOG_FILE"
else
    echo "Failed to start auto-restart daemon"
    rm -f "$DAEMON_PID_FILE"
    exit 1
fi
