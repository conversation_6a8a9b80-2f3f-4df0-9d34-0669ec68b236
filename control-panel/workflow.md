1. 客户端上报信息
```
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerInfo {
    pub server_name: String,
    pub exchange_name: String,
    pub timestamp: u64,
}
```
如果客户端链接断开之后了，重新链接，上报了同样的serverinfo，应该继续使用之前的状态显示
2. 给客户端发送信号
```
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SignalMessage {
    pub signal_type: SignalType,
}

/// API 请求/响应消息类型

/// 信号类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum SignalType {
    DelayOpen(u64),
    DirectOpen,
    DeplayClose(u64),
    DirectClose,
    ChangeSymbol(String),
}
```
发送信号的命令要简洁，
O 或者 o DirectOpen
DO 或者 do 或者Do 或者 dO 表示delayopen
c 或者 C表示close
DC 或者 dc 表示delayclose
3. 客户端上报仓位信息
```
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ReportMessage {
    AvaliableOpen(f64),   // 可开仓位
    CurrentPosition(f64), // 当前仓位
    StartOpen(u64),       // 开始建仓
    StartClose(u64),      // 开始平仓
}
```

其他要求
1. 显示客户端状态，链接是否正常
2. 如果有客户端状态不正常，在发送信号的信号的时候要给出警告，不能发送信号
3. 如果control-panel不小心重启了，应该可以继续显示之前的客户端列表，等待客户端重连
4. 要有一个 C 命令，表示 clear，清楚所有客户端状态，断开所有客户端链接
5. 还是支持一个命令  S  表示发送changesymbol
