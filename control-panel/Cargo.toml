[package]
name = "control-panel"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "control-panel"
path = "src/main.rs"

[[bin]]
name = "test_client"
path = "src/bin/test_client.rs"

[[bin]]
name = "reconnect_test_client"
path = "src/bin/reconnect_test_client.rs"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.21"
tungstenite = "0.21"
futures-util = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clap = { version = "4.0", features = ["derive"] }
uuid = { version = "1.0", features = ["v4"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-appender = "0.2"
anyhow = "1.0"
crossbeam-channel = "0.5"
ratatui = "0.24"
crossterm = "0.27"
rand = "0.8"
chrono = { version = "0.4", features = ["serde"] }
