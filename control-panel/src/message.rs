use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ServerMessage {
    #[serde(rename = "welcome")]
    Welcome {
        client_id: String,
        server_time: chrono::DateTime<chrono::Utc>,
    },
    #[serde(rename = "echo")]
    Echo {
        original_message: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    #[serde(rename = "broadcast")]
    Broadcast {
        message: String,
        timestamp: chrono::DateTime<chrono::Utc>,
        from_server: bool,
    },
    #[serde(rename = "set_symbol")]
    SetSymbol { symbol: String },
    #[serde(rename = "signal")]
    Signal {
        signal: SignalMessage,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    #[serde(rename = "disconnect")]
    Disconnect {
        reason: String,
        timestamp: chrono::DateTime<chrono::Utc>,
    },
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum ClientMessage {
    #[serde(rename = "ping")]
    Ping {
        timestamp: chrono::DateTime<chrono::Utc>,
    },
    #[serde(rename = "subscribe")]
    Subscribe { channels: Vec<String> },
    #[serde(rename = "unsubscribe")]
    Unsubscribe { channels: Vec<String> },
    #[serde(rename = "server_info")]
    ServerInfo {
        server_name: String,
        exchange_name: String,
        timestamp: u64,
    },
    #[serde(rename = "report")]
    Report { message: ReportMessage },
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PositionInfo {
    pub symbol: String,
    pub size: f64,
    pub entry_price: f64,
    pub current_price: f64,
    pub pnl: f64,
    pub percentage: f64,
}

impl PositionInfo {
    pub fn new(symbol: String, size: f64, entry_price: f64, current_price: f64) -> Self {
        let pnl = (current_price - entry_price) * size;
        let percentage = if entry_price != 0.0 {
            ((current_price - entry_price) / entry_price) * 100.0
        } else {
            0.0
        };

        Self {
            symbol,
            size,
            entry_price,
            current_price,
            pnl,
            percentage,
        }
    }
}

/// 信号消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SignalMessage {
    pub signal_type: SignalType,
}

/// 信号类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum SignalType {
    DelayOpenLong(u64),  // 毫秒级epoch时间戳
    DelayOpenShort(u64), // 毫秒级epoch时间戳
    DirectOpenLong,
    DirectOpenShort,
    DelayClose(u64), // 毫秒级epoch时间戳
    DirectClose,
    ChangeSymbol(String),
    Stop, // 停止信号
    Reverse,
}

/// 客户端上报消息枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReportMessage {
    AvailableLong(f64),
    AvailableShort(f64),
    CurrentLong(f64),
    CurrentShort(f64),
    StartOpen(u64),
    StartClose(u64),
    UnrealizedPnl(f64),
    Balance(f64),
}

/// 服务器信息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerInfo {
    pub server_name: String,
    pub exchange_name: String,
    pub timestamp: u64,
}
