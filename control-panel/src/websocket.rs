use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::{broadcast, mpsc, Mutex, RwLock};
use tokio_tungstenite::accept_async;
use tracing::{debug, error, info, instrument, warn};
use uuid::Uuid;

use crate::client_manager::{ClientInfo, ClientManager};
use crate::message::{ServerMessage, SignalMessage};

/// 客户端连接信息
#[derive(Debug)]
#[allow(dead_code)]
struct ClientConnection {
    id: String,
    addr: SocketAddr,
    sender: mpsc::UnboundedSender<ServerMessage>,
    connected_at: Instant,
}

/// 高性能WebSocket服务器
pub struct WebSocketServer {
    client_manager: Arc<Mutex<ClientManager>>,
    connections: Arc<RwLock<HashMap<String, ClientConnection>>>,
    signal_tx: broadcast::Sender<SignalMessage>,
    metrics: Arc<RwLock<ServerMetrics>>,
}

/// 消息广播器
pub struct MessageBroadcaster {
    signal_tx: broadcast::Sender<SignalMessage>,
    connections: Arc<RwLock<HashMap<String, ClientConnection>>>,
    metrics: Arc<RwLock<ServerMetrics>>,
}

/// 服务器性能指标
#[derive(Debug, Default)]
struct ServerMetrics {
    total_connections: u64,
    active_connections: u64,
    messages_sent: u64,
    messages_received: u64,
    signals_broadcast: u64,
    last_signal_time: Option<Instant>,
}

impl WebSocketServer {
    pub fn new(client_manager: Arc<Mutex<ClientManager>>) -> (Self, MessageBroadcaster) {
        let (signal_tx, _) = broadcast::channel(10000); // 增大缓冲区以支持更多客户端
        let connections = Arc::new(RwLock::new(HashMap::new()));
        let metrics = Arc::new(RwLock::new(ServerMetrics::default()));

        let broadcaster = MessageBroadcaster {
            signal_tx: signal_tx.clone(),
            connections: connections.clone(),
            metrics: metrics.clone(),
        };

        let server = Self {
            client_manager,
            connections,
            signal_tx,
            metrics,
        };

        (server, broadcaster)
    }

    #[instrument(skip(self), fields(host = %host, port = %port))]
    pub async fn start(&self, host: &str, port: u16) -> Result<()> {
        let addr = format!("{}:{}", host, port);
        let listener = TcpListener::bind(&addr).await?;
        info!("WebSocket server started successfully");

        // 启动性能监控任务
        let metrics_clone = self.metrics.clone();
        let connections_clone = self.connections.clone();
        tokio::spawn(async move {
            Self::metrics_reporter(metrics_clone, connections_clone).await;
        });

        while let Ok((stream, addr)) = listener.accept().await {
            let span = tracing::info_span!("client_connection", client_addr = %addr);
            let _enter = span.enter();

            debug!("New connection attempt from {}", addr);

            let client_manager = self.client_manager.clone();
            let connections = self.connections.clone();
            let signal_rx = self.signal_tx.subscribe();
            let metrics = self.metrics.clone();

            tokio::spawn(async move {
                if let Err(e) = Self::handle_connection(
                    stream,
                    addr,
                    client_manager,
                    connections,
                    signal_rx,
                    metrics,
                )
                .await
                {
                    error!("Connection error: {}", e);
                }
            });
        }

        Ok(())
    }

    /// 性能指标报告器
    async fn metrics_reporter(
        metrics: Arc<RwLock<ServerMetrics>>,
        connections: Arc<RwLock<HashMap<String, ClientConnection>>>,
    ) {
        let mut interval = tokio::time::interval(Duration::from_secs(30));

        loop {
            interval.tick().await;

            let metrics_guard = metrics.read().await;
            let connections_count = connections.read().await.len();

            info!(
                active_connections = connections_count,
                total_connections = metrics_guard.total_connections,
                messages_sent = metrics_guard.messages_sent,
                messages_received = metrics_guard.messages_received,
                signals_broadcast = metrics_guard.signals_broadcast,
                "Server performance metrics"
            );
        }
    }
}

impl MessageBroadcaster {
    /// 广播信号消息到所有客户端
    #[instrument(skip(self), fields(signal_type = ?signal.signal_type))]
    pub async fn broadcast_signal(&self, signal: SignalMessage) -> Result<()> {
        let start_time = Instant::now();

        // 更新指标
        {
            let mut metrics = self.metrics.write().await;
            metrics.signals_broadcast += 1;
            metrics.last_signal_time = Some(start_time);
        }

        // 发送信号到广播通道
        let sent_count = match self.signal_tx.send(signal.clone()) {
            Ok(count) => count,
            Err(_) => {
                warn!("No active signal receivers");
                return Ok(());
            }
        };

        info!(
            signal_type = ?signal.signal_type,
            receivers = sent_count,
            broadcast_duration_ms = start_time.elapsed().as_millis(),
            "Signal broadcast completed"
        );

        Ok(())
    }

    /// 发送消息到特定客户端
    #[allow(dead_code)]
    #[instrument(skip(self, message), fields(client_id = %client_id))]
    pub async fn send_to_client(&self, client_id: &str, message: ServerMessage) -> Result<()> {
        let connections = self.connections.read().await;

        if let Some(connection) = connections.get(client_id) {
            if let Err(_) = connection.sender.send(message) {
                warn!(client_id = %client_id, "Failed to send message to client - channel closed");
                return Err(anyhow::anyhow!("Client channel closed"));
            }

            // 更新指标
            {
                let mut metrics = self.metrics.write().await;
                metrics.messages_sent += 1;
            }

            debug!(client_id = %client_id, "Message sent to client");
        } else {
            warn!(client_id = %client_id, "Client not found");
            return Err(anyhow::anyhow!("Client not found"));
        }

        Ok(())
    }

    /// 获取当前连接的客户端数量
    #[allow(dead_code)]
    pub async fn get_client_count(&self) -> usize {
        self.connections.read().await.len()
    }

    /// 断开所有客户端连接
    pub async fn disconnect_all_clients(&self) -> usize {
        let mut connections = self.connections.write().await;
        let count = connections.len();

        // 向所有客户端发送断开连接消息
        for (client_id, connection) in connections.iter() {
            // 发送断开连接消息
            let disconnect_message = ServerMessage::Disconnect {
                reason: "Server is clearing all connections".to_string(),
                timestamp: chrono::Utc::now(),
            };

            if let Err(e) = connection.sender.send(disconnect_message) {
                debug!(
                    "Failed to send disconnect message to client {}: {}",
                    client_id, e
                );
            }
        }

        // 清除所有连接
        connections.clear();

        info!("Disconnected {} clients", count);
        count
    }
}

impl WebSocketServer {
    /// 处理单个客户端连接
    #[instrument(skip_all, fields(client_addr = %addr))]
    async fn handle_connection(
        stream: TcpStream,
        addr: SocketAddr,
        client_manager: Arc<Mutex<ClientManager>>,
        connections: Arc<RwLock<HashMap<String, ClientConnection>>>,
        mut signal_rx: broadcast::Receiver<SignalMessage>,
        metrics: Arc<RwLock<ServerMetrics>>,
    ) -> Result<()> {
        let ws_stream = accept_async(stream).await?;
        let (ws_sender, mut ws_receiver) = ws_stream.split();

        let client_id = Uuid::new_v4().to_string();
        let span = tracing::info_span!("client_session", client_id = %client_id);
        let _enter = span.enter();

        info!("Client connected successfully");

        // 更新连接指标
        {
            let mut metrics_guard = metrics.write().await;
            metrics_guard.total_connections += 1;
            metrics_guard.active_connections += 1;
        }

        // 创建消息通道
        let (msg_tx, msg_rx) = mpsc::unbounded_channel::<ServerMessage>();

        // 添加到连接管理器
        {
            let mut connections_guard = connections.write().await;
            connections_guard.insert(
                client_id.clone(),
                ClientConnection {
                    id: client_id.clone(),
                    addr,
                    sender: msg_tx.clone(),
                    connected_at: Instant::now(),
                },
            );
        }

        // 添加到客户端管理器
        let is_reconnect = {
            let mut manager = client_manager.lock().await;
            manager.add_client(ClientInfo {
                id: client_id.clone(),
                addr,
                connected_at: std::time::SystemTime::now(),
                server_info: None,
                last_report: None,
                reconnect_count: 0,
                last_seen: std::time::SystemTime::now(),
            })
        };

        if is_reconnect {
            info!("检测到客户端重连");
        }

        // 发送欢迎消息
        let welcome_msg = ServerMessage::Welcome {
            client_id: client_id.clone(),
            server_time: chrono::Utc::now(),
        };

        if let Err(_) = msg_tx.send(welcome_msg) {
            error!("Failed to send welcome message");
            return Ok(());
        }

        // 启动消息发送任务
        let ws_sender = Arc::new(Mutex::new(ws_sender));
        let sender_task = {
            let ws_sender = ws_sender.clone();
            let client_id = client_id.clone();
            let metrics = metrics.clone();

            tokio::spawn(async move {
                Self::message_sender_task(ws_sender, msg_rx, client_id, metrics).await;
            })
        };

        // 主消息处理循环
        let result = Self::message_handler_loop(
            &mut ws_receiver,
            &mut signal_rx,
            &client_id,
            &client_manager,
            &msg_tx,
            &metrics,
        )
        .await;

        // 清理连接
        sender_task.abort();

        {
            let mut connections_guard = connections.write().await;
            connections_guard.remove(&client_id);
        }

        {
            let mut manager = client_manager.lock().await;
            manager.remove_client(&client_id);
        }

        {
            let mut metrics_guard = metrics.write().await;
            metrics_guard.active_connections = metrics_guard.active_connections.saturating_sub(1);
        }

        info!("Client session ended");
        result
    }

    /// 消息发送任务
    #[instrument(skip_all, fields(client_id = %client_id))]
    async fn message_sender_task(
        ws_sender: Arc<
            Mutex<
                futures_util::stream::SplitSink<
                    tokio_tungstenite::WebSocketStream<TcpStream>,
                    tokio_tungstenite::tungstenite::Message,
                >,
            >,
        >,
        mut msg_rx: mpsc::UnboundedReceiver<ServerMessage>,
        client_id: String,
        metrics: Arc<RwLock<ServerMetrics>>,
    ) {
        use tokio_tungstenite::tungstenite::Message;

        while let Some(server_msg) = msg_rx.recv().await {
            // 检查是否是断开连接消息
            let is_disconnect = matches!(server_msg, ServerMessage::Disconnect { .. });

            let json_msg = match serde_json::to_string(&server_msg) {
                Ok(json) => json,
                Err(e) => {
                    error!("Failed to serialize message: {}", e);
                    continue;
                }
            };

            let mut sender = ws_sender.lock().await;
            if let Err(e) = sender.send(Message::Text(json_msg)).await {
                error!("Failed to send message to client: {}", e);
                break;
            }

            // 更新发送指标
            {
                let mut metrics_guard = metrics.write().await;
                metrics_guard.messages_sent += 1;
            }

            debug!("Message sent to client");

            // 如果是断开连接消息，发送Close帧并退出
            if is_disconnect {
                debug!("Sending disconnect message, closing connection");
                if let Err(e) = sender.send(Message::Close(None)).await {
                    error!("Failed to send close frame: {}", e);
                }
                break;
            }
        }

        debug!("Message sender task ended");
    }

    /// 消息处理循环
    #[instrument(skip_all, fields(client_id = %client_id))]
    async fn message_handler_loop(
        ws_receiver: &mut futures_util::stream::SplitStream<
            tokio_tungstenite::WebSocketStream<TcpStream>,
        >,
        signal_rx: &mut broadcast::Receiver<SignalMessage>,
        client_id: &str,
        client_manager: &Arc<Mutex<ClientManager>>,
        msg_tx: &mpsc::UnboundedSender<ServerMessage>,
        metrics: &Arc<RwLock<ServerMetrics>>,
    ) -> Result<()> {
        use tokio_tungstenite::tungstenite::Message;

        loop {
            tokio::select! {
                // 处理来自客户端的消息
                ws_msg = ws_receiver.next() => {
                    match ws_msg {
                        Some(Ok(Message::Text(text))) => {
                            debug!("Received message from client");

                            // 更新接收指标
                            {
                                let mut metrics_guard = metrics.write().await;
                                metrics_guard.messages_received += 1;
                            }

                            // 处理客户端消息
                            if let Err(e) = Self::handle_client_message(
                                &text,
                                client_id,
                                client_manager,
                                msg_tx
                            ).await {
                                warn!("Error handling client message: {}", e);
                            }
                        }
                        Some(Ok(Message::Close(_))) => {
                            info!("Client initiated close");
                            break;
                        }
                        Some(Ok(Message::Ping(_payload))) => {
                            if let Err(_) = msg_tx.send(ServerMessage::Echo {
                                original_message: "pong".to_string(),
                                timestamp: chrono::Utc::now(),
                            }) {
                                warn!("Failed to send pong response");
                            }
                        }
                        Some(Err(e)) => {
                            error!("WebSocket error: {}", e);
                            break;
                        }
                        None => {
                            info!("WebSocket stream ended");
                            break;
                        }
                        _ => {}
                    }
                }
                // 处理信号广播
                signal_msg = signal_rx.recv() => {
                    match signal_msg {
                        Ok(signal) => {
                            debug!(signal_type = ?signal.signal_type, "Received signal for broadcast");

                            let signal_msg = ServerMessage::Signal {
                                signal,
                                timestamp: chrono::Utc::now(),
                            };

                            if let Err(_) = msg_tx.send(signal_msg) {
                                warn!("Failed to send signal to client");
                                break;
                            }
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            warn!(skipped_messages = skipped, "Client lagged behind on signals");
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Signal broadcast channel closed");
                            break;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// 处理客户端消息
    #[instrument(skip_all, fields(client_id = %client_id))]
    async fn handle_client_message(
        message_text: &str,
        client_id: &str,
        client_manager: &Arc<Mutex<ClientManager>>,
        msg_tx: &mpsc::UnboundedSender<ServerMessage>,
    ) -> Result<()> {
        use crate::message::{ClientMessage, ReportMessage, ServerInfo};

        // 尝试解析客户端消息
        let client_msg: ClientMessage = match serde_json::from_str(message_text) {
            Ok(msg) => msg,
            Err(e) => {
                warn!("Failed to parse client message: {}", e);
                // 发送错误响应
                let error_response = ServerMessage::Echo {
                    original_message: format!("Error parsing message: {}", e),
                    timestamp: chrono::Utc::now(),
                };
                let _ = msg_tx.send(error_response);
                return Ok(());
            }
        };

        match client_msg {
            ClientMessage::Ping { timestamp } => {
                debug!("Received ping from client");
                let pong_response = ServerMessage::Echo {
                    original_message: format!("pong - received ping at {}", timestamp),
                    timestamp: chrono::Utc::now(),
                };
                let _ = msg_tx.send(pong_response);
            }

            ClientMessage::ServerInfo {
                server_name,
                exchange_name,
                timestamp,
            } => {
                info!(
                    server_name = %server_name,
                    exchange_name = %exchange_name,
                    timestamp = timestamp,
                    "Received server info from client"
                );

                // 更新客户端管理器中的服务器信息
                {
                    let mut manager = client_manager.lock().await;
                    manager.update_server_info(
                        client_id,
                        ServerInfo {
                            server_name: server_name.clone(),
                            exchange_name: exchange_name.clone(),
                            timestamp,
                        },
                    );
                }

                // 发送确认响应
                let ack_response = ServerMessage::Echo {
                    original_message: format!(
                        "Server info received: {} on {}",
                        server_name, exchange_name
                    ),
                    timestamp: chrono::Utc::now(),
                };
                let _ = msg_tx.send(ack_response);
            }

            ClientMessage::Report { message } => {
                match &message {
                    ReportMessage::AvailableLong(amount) => {
                        info!(amount = %amount, "Client reported available long position");
                    }
                    ReportMessage::AvailableShort(amount) => {
                        info!(amount = %amount, "Client reported available short position");
                    }
                    ReportMessage::CurrentLong(position) => {
                        info!(position = %position, "Client reported current long position");
                    }
                    ReportMessage::CurrentShort(position) => {
                        info!(position = %position, "Client reported current short position");
                    }
                    ReportMessage::StartOpen(timestamp) => {
                        info!(timestamp = %timestamp, "Client started opening position");
                    }
                    ReportMessage::StartClose(timestamp) => {
                        info!(timestamp = %timestamp, "Client started closing position");
                    }
                    ReportMessage::Balance(amount) => {
                        info!(amount = %amount, "Client reported balance");
                    }
                    ReportMessage::UnrealizedPnl(amount) => {
                        info!(amount = %amount, "Client reported unrealized PnL");
                    }
                }

                // 更新客户端管理器中的报告信息
                {
                    let mut manager = client_manager.lock().await;
                    manager.update_report(client_id, message.clone());
                }

                // 发送确认响应
                let ack_response = ServerMessage::Echo {
                    original_message: "Report received".to_string(),
                    timestamp: chrono::Utc::now(),
                };
                let _ = msg_tx.send(ack_response);
            }

            ClientMessage::Subscribe { channels } => {
                info!(channels = ?channels, "Client subscribed to channels");
                let ack_response = ServerMessage::Echo {
                    original_message: format!("Subscribed to {} channels", channels.len()),
                    timestamp: chrono::Utc::now(),
                };
                let _ = msg_tx.send(ack_response);
            }

            ClientMessage::Unsubscribe { channels } => {
                info!(channels = ?channels, "Client unsubscribed from channels");
                let ack_response = ServerMessage::Echo {
                    original_message: format!("Unsubscribed from {} channels", channels.len()),
                    timestamp: chrono::Utc::now(),
                };
                let _ = msg_tx.send(ack_response);
            }
        }

        Ok(())
    }
}
