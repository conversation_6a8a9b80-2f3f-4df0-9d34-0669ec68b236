use anyhow::Result;
use futures_util::{SinkExt, StreamExt};

use std::time::{SystemTime, UNIX_EPOCH};
use tokio::time::{sleep, Duration};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use tracing::info;

// 导入消息类型
use control_panel::message::{ClientMessage, ReportMessage};

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::fmt::init();

    let server_name = std::env::args()
        .nth(1)
        .unwrap_or_else(|| "Persistent Trading Bot".to_string());

    let exchange_name = std::env::args()
        .nth(2)
        .unwrap_or_else(|| "Binance".to_string());

    info!("启动持久化测试客户端: {} on {}", server_name, exchange_name);

    // 模拟多次重连
    for reconnect_count in 1..=5 {
        info!("第 {} 次连接尝试", reconnect_count);

        match connect_and_run(&server_name, &exchange_name, reconnect_count).await {
            Ok(_) => info!("连接 {} 正常结束", reconnect_count),
            Err(e) => info!("连接 {} 出错: {}", reconnect_count, e),
        }

        // 等待一段时间再重连
        let wait_time = 3 + reconnect_count * 2;
        info!("等待 {} 秒后重连...", wait_time);
        sleep(Duration::from_secs(wait_time as u64)).await;
    }

    info!("重连测试完成");
    Ok(())
}

async fn connect_and_run(
    server_name: &str,
    exchange_name: &str,
    reconnect_count: u32,
) -> Result<()> {
    let url = "ws://127.0.0.1:8181";
    info!("连接到 {}", url);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // 启动消息接收任务
    let read_task = tokio::spawn(async move {
        while let Some(msg) = read.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    info!("收到消息: {}", text);

                    // 检查是否收到信号
                    if text.contains("\"type\":\"signal\"") {
                        info!("🎯 收到交易信号！");
                    }
                }
                Ok(Message::Close(_)) => {
                    info!("服务器关闭连接");
                    break;
                }
                Err(e) => {
                    info!("接收消息错误: {}", e);
                    break;
                }
                _ => {}
            }
        }
    });

    // 发送服务器信息（用于状态恢复）
    let server_info_msg = ClientMessage::ServerInfo {
        server_name: server_name.to_string(),
        exchange_name: exchange_name.to_string(),
        timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
    };

    let json_msg = serde_json::to_string(&server_info_msg)?;
    write.send(Message::Text(json_msg)).await?;
    info!("发送服务器信息");

    sleep(Duration::from_secs(1)).await;

    // 发送不同的报告信息（模拟状态变化）
    let reports = vec![
        ReportMessage::AvailableLong(1000.0 + reconnect_count as f64 * 100.0),
        ReportMessage::AvailableShort(500.0 + reconnect_count as f64 * 50.0),
        ReportMessage::CurrentLong(reconnect_count as f64 * 0.1),
        ReportMessage::StartOpen(SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs()),
    ];

    for (i, report) in reports.iter().enumerate() {
        let report_msg = ClientMessage::Report {
            message: report.clone(),
        };

        let json_msg = serde_json::to_string(&report_msg)?;
        write.send(Message::Text(json_msg)).await?;
        info!("发送报告 {}: {:?}", i + 1, report);

        sleep(Duration::from_secs(1)).await;
    }

    // 保持连接一段时间，等待可能的信号
    let connection_time = 10 + reconnect_count * 2;
    info!("保持连接，等待信号... ({}秒)", connection_time);

    tokio::select! {
        _ = read_task => {
            info!("读取任务完成");
        }
        _ = sleep(Duration::from_secs(connection_time as u64)) => {
            info!("连接超时，主动断开");
        }
    }

    Ok(())
}
