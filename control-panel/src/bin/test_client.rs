use anyhow::Result;
use futures_util::{SinkExt, StreamExt};
use serde_json;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::time::{sleep, Duration};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use tracing::info;

// 导入消息类型
use control_panel::message::{ClientMessage, ReportMessage};

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::fmt::init();

    let url = "ws://127.0.0.1:8181";
    info!("Connecting to {}", url);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // 启动消息接收任务
    let read_task = tokio::spawn(async move {
        while let Some(msg) = read.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    info!("Received: {}", text);
                }
                Ok(Message::Close(_)) => {
                    info!("Connection closed by server");
                    break;
                }
                Err(e) => {
                    info!("Error receiving message: {}", e);
                    break;
                }
                _ => {}
            }
        }
    });

    // 发送服务器信息
    let server_info_msg = ClientMessage::ServerInfo {
        server_name: "Test Trading Bot".to_string(),
        exchange_name: "Binance".to_string(),
        timestamp: SystemTime::now().duration_since(UNIX_EPOCH)?.as_secs(),
    };

    let json_msg = serde_json::to_string(&server_info_msg)?;
    write.send(Message::Text(json_msg)).await?;
    info!("Sent server info");

    sleep(Duration::from_secs(1)).await;

    // 发送可用多头开仓金额报告
    let report_msg = ClientMessage::Report {
        message: ReportMessage::AvailableLong(1000.0),
    };

    let json_msg = serde_json::to_string(&report_msg)?;
    write.send(Message::Text(json_msg)).await?;
    info!("Sent available long report");

    sleep(Duration::from_secs(1)).await;

    // 发送当前多头仓位报告
    let position_msg = ClientMessage::Report {
        message: ReportMessage::CurrentLong(0.5),
    };

    let json_msg = serde_json::to_string(&position_msg)?;
    write.send(Message::Text(json_msg)).await?;
    info!("Sent current position report");

    // 保持连接，等待信号
    info!("Waiting for signals from server... (will run for 30 seconds)");

    // 等待读取任务完成或者30秒后退出
    tokio::select! {
        _ = read_task => {
            info!("Read task completed");
        }
        _ = sleep(Duration::from_secs(30)) => {
            info!("Test completed after 30 seconds");
        }
    }

    Ok(())
}
