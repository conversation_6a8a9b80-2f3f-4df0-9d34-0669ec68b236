use anyhow::Result;
use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEventKind},
    execute,
    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},
};
use ratatui::{
    backend::{Backend, CrosstermBackend},
    layout::{Alignment, Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span},
    widgets::{Block, Borders, Cell, List, ListItem, Paragraph, Row, Table},
    Frame, Terminal,
};
use std::collections::HashMap;
use std::io;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use tokio::sync::Mutex;

use crate::client_manager::ClientManager;
use crate::message::{PositionInfo, SignalMessage, SignalType};
use crate::websocket::MessageBroadcaster;

pub struct TuiApp {
    client_manager: Arc<Mutex<ClientManager>>,
    broadcaster: MessageBroadcaster,
    input: String,
    input_mode: InputMode,
    messages: Vec<String>,
    positions: HashMap<String, PositionInfo>,
    selected_tab: usize,
    should_quit: bool,
    last_update: Instant,
    // 状态提示相关
    status_message: Option<String>,
    status_message_type: StatusType,
    status_timer: Instant,
}

#[derive(Clone, Copy)]
enum StatusType {
    Success,
    Warning,
    Error,
    Info,
}

#[derive(PartialEq)]
enum InputMode {
    Normal,
    Editing,
}

impl TuiApp {
    pub fn new(client_manager: Arc<Mutex<ClientManager>>, broadcaster: MessageBroadcaster) -> Self {
        // Sample position data for demonstration
        let mut positions = HashMap::new();
        positions.insert(
            "BTC/USDT".to_string(),
            PositionInfo::new("BTC/USDT".to_string(), 0.5, 45000.0, 47000.0),
        );
        positions.insert(
            "ETH/USDT".to_string(),
            PositionInfo::new("ETH/USDT".to_string(), 2.0, 3000.0, 3200.0),
        );

        Self {
            client_manager,
            broadcaster,
            input: String::new(),
            input_mode: InputMode::Normal,
            messages: vec![
                "🚀 高性能WebSocket控制面板已启动".to_string(),
                "📁 详细日志输出到: logs/control-panel.log".to_string(),
                "".to_string(),
                "⌨️  操作说明:".to_string(),
                "  'q' - 退出程序".to_string(),
                "  'i' - 进入输入模式".to_string(),
                "  Tab - 切换标签页".to_string(),
                "  Esc - 退出输入模式".to_string(),
                "".to_string(),
                "📡 简化信号命令:".to_string(),
                "  OL - 立即开多 (DirectOpenLong)".to_string(),
                "  OS - 立即开空 (DirectOpenShort)".to_string(),
                "  C/c - 立即平仓 (DirectClose)".to_string(),
                "  DOL <秒数> - 延迟开多 (DelayOpenLong)".to_string(),
                "  DOS <秒数> - 延迟开空 (DelayOpenShort)".to_string(),
                "  DC <秒数> - 延迟平仓 (DelayClose)".to_string(),
                "  S <交易对> - 切换交易对 (ChangeSymbol)".to_string(),
                "  STOP - 停止信号 (Stop)".to_string(),
                "  R - 反向信号 (Reverse)".to_string(),
                "  change_symbol <交易对> - 切换交易对 (完整格式)".to_string(),
                "  CLEAR - 清除所有客户端状态".to_string(),
                "".to_string(),
                "⚠️  发送信号前会检查客户端状态".to_string(),
                "🎯 所有信号将同时发送给在线客户端".to_string(),
                "📊 查看详细日志: tail -f logs/control-panel.log".to_string(),
            ],
            positions,
            selected_tab: 0,
            should_quit: false,
            last_update: Instant::now(),
            // 初始化状态提示
            status_message: None,
            status_message_type: StatusType::Info,
            status_timer: Instant::now(),
        }
    }

    /// 设置状态消息
    fn set_status_message(&mut self, message: String, status_type: StatusType) {
        self.status_message = Some(message);
        self.status_message_type = status_type;
        self.status_timer = Instant::now();
    }

    /// 检查状态消息是否应该隐藏（3秒后自动隐藏）
    fn should_hide_status(&self) -> bool {
        if self.status_message.is_some() {
            self.status_timer.elapsed().as_secs() > 3
        } else {
            true
        }
    }

    pub async fn run(&mut self) -> Result<()> {
        // Setup terminal
        enable_raw_mode()?;
        let mut stdout = io::stdout();
        execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
        let backend = CrosstermBackend::new(stdout);
        let mut terminal = Terminal::new(backend)?;

        let result = self.run_app(&mut terminal).await;

        // Restore terminal
        disable_raw_mode()?;
        execute!(
            terminal.backend_mut(),
            LeaveAlternateScreen,
            DisableMouseCapture
        )?;
        terminal.show_cursor()?;

        result
    }

    async fn run_app<B: Backend>(&mut self, terminal: &mut Terminal<B>) -> Result<()> {
        loop {
            terminal.draw(|f| self.ui(f))?;

            // Handle events with timeout
            if event::poll(Duration::from_millis(100))? {
                if let Event::Key(key) = event::read()? {
                    if key.kind == KeyEventKind::Press {
                        match self.input_mode {
                            InputMode::Normal => match key.code {
                                KeyCode::Char('q') => {
                                    self.should_quit = true;
                                }
                                KeyCode::Char('i') => {
                                    self.input_mode = InputMode::Editing;
                                }
                                KeyCode::Tab => {
                                    self.selected_tab = (self.selected_tab + 1) % 3;
                                }
                                _ => {}
                            },
                            InputMode::Editing => match key.code {
                                KeyCode::Enter => {
                                    self.send_message().await;
                                }
                                KeyCode::Char(c) => {
                                    self.input.push(c);
                                }
                                KeyCode::Backspace => {
                                    self.input.pop();
                                }
                                KeyCode::Esc => {
                                    self.input_mode = InputMode::Normal;
                                }
                                _ => {}
                            },
                        }
                    }
                }
            }

            // Update positions periodically (simulate real-time updates)
            if self.last_update.elapsed() > Duration::from_secs(5) {
                self.update_positions();
                self.last_update = Instant::now();
            }

            if self.should_quit {
                break;
            }
        }
        Ok(())
    }

    fn ui(&mut self, f: &mut Frame) {
        // 清除过期的状态消息
        if self.should_hide_status() {
            self.status_message = None;
        }

        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .margin(1)
            .constraints([
                Constraint::Length(3),
                Constraint::Min(0),
                Constraint::Length(3),
            ])
            .split(f.size());

        // Header
        self.render_header(f, chunks[0]);

        // Main content based on selected tab
        match self.selected_tab {
            0 => self.render_clients_tab(f, chunks[1]),
            1 => self.render_positions_tab(f, chunks[1]),
            2 => self.render_messages_tab(f, chunks[1]),
            _ => {}
        }

        // Input area
        self.render_input(f, chunks[2]);

        // 渲染状态消息（如果有的话）
        self.render_status_message(f);
    }

    fn render_header(&self, f: &mut Frame, area: Rect) {
        let tabs = vec!["客户端连接", "交易所仓位", "操作指示"];
        let selected_style = Style::default()
            .fg(Color::Black)
            .bg(Color::Yellow)
            .add_modifier(Modifier::BOLD);
        let normal_style = Style::default().fg(Color::White);

        // 创建一行显示所有标签页，用 | 分隔
        let mut spans = Vec::new();
        for (i, &tab) in tabs.iter().enumerate() {
            let style = if i == self.selected_tab {
                selected_style
            } else {
                normal_style
            };

            spans.push(Span::styled(tab, style));

            // 添加分隔符，除了最后一个
            if i < tabs.len() - 1 {
                spans.push(Span::styled(" | ", normal_style));
            }
        }

        let header = Paragraph::new(Line::from(spans))
            .block(Block::default().borders(Borders::ALL))
            .alignment(Alignment::Center);
        f.render_widget(header, area);
    }

    fn render_clients_tab(&self, f: &mut Frame, area: Rect) {
        // 客户端连接状态表格
        let header = vec![
            "客户端名称",
            "交易所",
            "连接状态",
            "重连次数",
            "首次连接",
            "最后活跃",
        ];

        // 从client_manager获取真实数据
        let rows: Vec<Row> = if let Ok(manager) = self.client_manager.try_lock() {
            let states = manager.get_persistent_states();
            states
                .iter()
                .map(|(server_name, state)| {
                    let status = if state.current_session_id.is_some() {
                        Span::styled("在线", Style::default().fg(Color::Green))
                    } else {
                        Span::styled("离线", Style::default().fg(Color::Red))
                    };

                    let first_seen = format_time_ago(state.first_seen);
                    let last_seen = format_time_ago(state.last_seen);

                    Row::new(vec![
                        Cell::from(server_name.clone()),
                        Cell::from(state.exchange_name.clone()),
                        Cell::from(status),
                        Cell::from(state.total_connections.to_string()),
                        Cell::from(first_seen),
                        Cell::from(last_seen),
                    ])
                })
                .collect()
        } else {
            vec![]
        };

        let table = Table::new(rows)
            .header(Row::new(header).style(Style::default().add_modifier(Modifier::BOLD)))
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .title("客户端连接状态"),
            )
            .widths(&[
                Constraint::Length(15), // 客户端名称
                Constraint::Length(10), // 交易所
                Constraint::Length(10), // 连接状态
                Constraint::Length(10), // 重连次数
                Constraint::Length(12), // 首次连接
                Constraint::Length(12), // 最后活跃
            ]);

        f.render_widget(table, area);
    }

    fn render_positions_tab(&self, f: &mut Frame, area: Rect) {
        // 按交易所聚合的仓位情况
        let header = vec![
            "交易所",
            "多头可开",
            "空头可开",
            "多头仓位",
            "空头仓位",
            "余额",
            "未实现盈亏",
            "最后更新",
        ];

        // 从client_manager获取真实数据
        let rows: Vec<Row> = if let Ok(manager) = self.client_manager.try_lock() {
            let exchange_summary = manager.get_exchange_summary();

            // 按交易所名称字母顺序排序
            let mut sorted_exchanges: Vec<_> = exchange_summary.iter().collect();
            sorted_exchanges.sort_by(|a, b| a.0.cmp(b.0));

            sorted_exchanges
                .iter()
                .map(|(exchange_name, summary)| {
                    let last_update = format_time_ago(summary.last_update);

                    Row::new(vec![
                        Cell::from((*exchange_name).clone()),
                        Cell::from(format!("{:.2}", summary.available_long)),
                        Cell::from(format!("{:.2}", summary.available_short)),
                        Cell::from(format!("{:.4}", summary.current_long)),
                        Cell::from(format!("{:.4}", summary.current_short)),
                        Cell::from(format!("{:.2}", summary.balance)),
                        Cell::from(format!("{:.4}", summary.unrealized_pnl)),
                        Cell::from(last_update),
                    ])
                })
                .collect()
        } else {
            vec![]
        };

        let table = Table::new(rows)
            .header(Row::new(header).style(Style::default().add_modifier(Modifier::BOLD)))
            .block(
                Block::default()
                    .borders(Borders::ALL)
                    .title("交易所仓位汇总"),
            )
            .widths(&[
                Constraint::Length(8),  // 交易所
                Constraint::Length(10), // 多头可开
                Constraint::Length(10), // 空头可开
                Constraint::Length(12), // 多头仓位
                Constraint::Length(12), // 空头仓位
                Constraint::Length(12), // 余额
                Constraint::Length(12), // 未实现盈亏
                Constraint::Length(12), // 最后更新
            ]);

        f.render_widget(table, area);
    }

    fn render_messages_tab(&self, f: &mut Frame, area: Rect) {
        // 分割区域为两部分：操作指示和操作日志
        let chunks = Layout::default()
            .direction(Direction::Vertical)
            .constraints([Constraint::Percentage(70), Constraint::Percentage(30)])
            .split(area);

        // 操作指示部分
        let instructions = vec![
            ListItem::new("📋 操作指示"),
            ListItem::new(""),
            ListItem::new("🎮 快捷命令:"),
            ListItem::new("  OL - 立即开多 (DirectOpenLong)"),
            ListItem::new("  OS - 立即开空 (DirectOpenShort)"),
            ListItem::new("  C/c - 立即平仓 (DirectClose)"),
            ListItem::new("  DOL <秒数> - 延迟开多 (DelayOpenLong)"),
            ListItem::new("  DOS <秒数> - 延迟开空 (DelayOpenShort)"),
            ListItem::new("  DC <秒数> - 延迟平仓 (DelayClose)"),
            ListItem::new("  S <交易对> - 切换交易对 (ChangeSymbol)"),
            ListItem::new("  STOP - 停止信号 (Stop)"),
            ListItem::new("  R - 反向信号 (Reverse)"),
            ListItem::new("  CLEAR - 清除所有客户端状态"),
            ListItem::new(""),
            ListItem::new("📝 完整命令格式:"),
            ListItem::new("  change_symbol <交易对> - 切换交易对"),
            ListItem::new("  delay_open_long <秒数> - 延迟开多"),
            ListItem::new("  delay_open_short <秒数> - 延迟开空"),
            ListItem::new("  delay_close <秒数> - 延迟平仓"),
            ListItem::new("  direct_open_long - 立即开多"),
            ListItem::new("  direct_open_short - 立即开空"),
            ListItem::new("  direct_close - 立即平仓"),
            ListItem::new("  stop - 停止信号"),
            ListItem::new("  reverse - 反向信号"),
            ListItem::new(""),
            ListItem::new("⚠️  注意事项:"),
            ListItem::new("  • 发送信号前会自动检查客户端状态"),
            ListItem::new("  • 如有客户端离线，会显示警告并阻止发送"),
            ListItem::new("  • 所有信号将同时发送给在线客户端"),
            ListItem::new("  • 使用CLEAR命令会断开所有连接"),
            ListItem::new(""),
            ListItem::new("🔧 操作步骤:"),
            ListItem::new("  1. 按 'i' 进入输入模式"),
            ListItem::new("  2. 输入命令 (如: O, DO 5, S BTCUSDT)"),
            ListItem::new("  3. 按 Enter 发送命令"),
            ListItem::new("  4. 按 Esc 退出输入模式"),
        ];

        let instructions_list = List::new(instructions)
            .block(Block::default().borders(Borders::ALL).title("操作指示"))
            .style(Style::default().fg(Color::White));

        f.render_widget(instructions_list, chunks[0]);

        // 操作日志部分
        let messages: Vec<ListItem> = self
            .messages
            .iter()
            .map(|m| ListItem::new(m.clone()))
            .collect();

        let messages_list = List::new(messages)
            .block(Block::default().borders(Borders::ALL).title("操作日志"))
            .style(Style::default().fg(Color::White));

        f.render_widget(messages_list, chunks[1]);
    }

    fn render_status_message(&self, f: &mut Frame) {
        // 检查是否应该隐藏状态消息
        if self.should_hide_status() {
            return;
        }

        if let Some(ref message) = self.status_message {
            let (bg_color, fg_color) = match self.status_message_type {
                StatusType::Success => (Color::Green, Color::White),
                StatusType::Warning => (Color::Yellow, Color::Black),
                StatusType::Error => (Color::Red, Color::White),
                StatusType::Info => (Color::Blue, Color::White),
            };

            // 计算弹窗位置（屏幕中央）
            let area = f.size();
            let popup_width = (message.len() + 4).min(area.width as usize - 4) as u16;
            let popup_height = 3;

            let popup_area = Rect {
                x: (area.width.saturating_sub(popup_width)) / 2,
                y: (area.height.saturating_sub(popup_height)) / 2,
                width: popup_width,
                height: popup_height,
            };

            // 清除背景
            let clear_block = Block::default().style(Style::default().bg(bg_color));
            f.render_widget(clear_block, popup_area);

            // 渲染状态消息
            let status_paragraph = Paragraph::new(message.clone())
                .style(
                    Style::default()
                        .fg(fg_color)
                        .bg(bg_color)
                        .add_modifier(Modifier::BOLD),
                )
                .alignment(Alignment::Center)
                .block(
                    Block::default()
                        .borders(Borders::ALL)
                        .style(Style::default().fg(fg_color).bg(bg_color)),
                );

            f.render_widget(status_paragraph, popup_area);
        }
    }

    fn render_input(&self, f: &mut Frame, area: Rect) {
        let input_style = match self.input_mode {
            InputMode::Normal => Style::default(),
            InputMode::Editing => Style::default().fg(Color::Yellow),
        };

        let input = Paragraph::new(self.input.as_str())
            .style(input_style)
            .block(Block::default().borders(Borders::ALL).title("Input"));
        f.render_widget(input, area);

        if self.input_mode == InputMode::Editing {
            f.set_cursor(area.x + self.input.len() as u16 + 1, area.y + 1);
        }
    }

    async fn send_message(&mut self) {
        if !self.input.is_empty() {
            let message = self.input.clone();

            // 检查是否是Clear命令
            if message.trim().to_uppercase() == "CLEAR" {
                self.handle_clear_command().await;
                self.set_status_message("🧹 所有客户端状态已清除".to_string(), StatusType::Success);
            } else if let Some(signal) = self.parse_signal_command(&message) {
                // 检查客户端状态
                if self.check_client_status().await {
                    // 发送信号到所有客户端
                    if let Err(e) = self.broadcaster.broadcast_signal(signal.clone()).await {
                        self.messages
                            .push(format!("❌ Error sending signal: {}", e));
                        self.set_status_message(format!("❌ 发送失败: {}", e), StatusType::Error);
                    } else {
                        self.messages
                            .push(format!("✅ Signal broadcasted: {:?}", signal.signal_type));
                        let status_msg = match &signal.signal_type {
                            SignalType::DelayOpenLong(timestamp_ms) => {
                                let target_time =
                                    std::time::UNIX_EPOCH + Duration::from_millis(*timestamp_ms);
                                let delay_secs = target_time
                                    .duration_since(SystemTime::now())
                                    .unwrap_or_default()
                                    .as_secs();
                                format!("✅ 延迟开多信号已发送 ({}秒后执行)", delay_secs)
                            }
                            SignalType::DelayOpenShort(timestamp_ms) => {
                                let target_time =
                                    std::time::UNIX_EPOCH + Duration::from_millis(*timestamp_ms);
                                let delay_secs = target_time
                                    .duration_since(SystemTime::now())
                                    .unwrap_or_default()
                                    .as_secs();
                                format!("✅ 延迟开空信号已发送 ({}秒后执行)", delay_secs)
                            }
                            SignalType::DelayClose(timestamp_ms) => {
                                let target_time =
                                    std::time::UNIX_EPOCH + Duration::from_millis(*timestamp_ms);
                                let delay_secs = target_time
                                    .duration_since(SystemTime::now())
                                    .unwrap_or_default()
                                    .as_secs();
                                format!("✅ 延迟平仓信号已发送 ({}秒后执行)", delay_secs)
                            }
                            SignalType::DirectOpenLong => "✅ 立即开多信号已发送".to_string(),
                            SignalType::DirectOpenShort => "✅ 立即开空信号已发送".to_string(),
                            SignalType::DirectClose => "✅ 立即平仓信号已发送".to_string(),
                            SignalType::ChangeSymbol(symbol) => {
                                format!("✅ 切换交易对信号已发送: {}", symbol)
                            }
                            SignalType::Stop => "✅ 停止信号已发送".to_string(),
                            SignalType::Reverse => "✅ 反向信号已发送".to_string(),
                        };
                        self.set_status_message(status_msg, StatusType::Success);
                    }
                } else {
                    self.messages.push(
                        "⚠️  Warning: Some clients are offline. Signal not sent.".to_string(),
                    );
                    self.set_status_message(
                        "⚠️ 有客户端离线，信号未发送".to_string(),
                        StatusType::Warning,
                    );
                }
            } else {
                self.messages.push(format!(
                    "❌ Invalid command: {}. Available commands:",
                    message
                ));
                self.messages.push("  OL - Direct Open Long".to_string());
                self.messages.push("  OS - Direct Open Short".to_string());
                self.messages.push("  C/c - Direct Close".to_string());
                self.messages
                    .push("  DOL <seconds> - Delay Open Long".to_string());
                self.messages
                    .push("  DOS <seconds> - Delay Open Short".to_string());
                self.messages
                    .push("  DC <seconds> - Delay Close".to_string());
                self.messages
                    .push("  S <symbol> - Change Symbol".to_string());
                self.messages.push("  STOP - Stop Signal".to_string());
                self.messages.push("  R - Reverse Signal".to_string());
                self.messages
                    .push("  change_symbol <symbol> - Change Symbol (full format)".to_string());
                self.messages
                    .push("  CLEAR - Clear all client states".to_string());
                self.set_status_message(format!("❌ 无效命令: {}", message), StatusType::Error);
            }

            self.input.clear();
            self.input_mode = InputMode::Normal;
        }
    }

    /// 检查客户端状态
    async fn check_client_status(&mut self) -> bool {
        let manager = self.client_manager.lock().await;
        let all_online = manager.all_clients_online();

        if !all_online {
            let offline_clients = manager.get_offline_clients();
            self.messages.push(format!(
                "⚠️  Offline clients: {}",
                offline_clients.join(", ")
            ));
        }

        all_online
    }

    /// 处理Clear命令
    async fn handle_clear_command(&mut self) {
        self.messages
            .push("🧹 Clearing all client states...".to_string());

        // 1. 首先发送Stop信号给所有客户端
        let stop_signal = SignalMessage {
            signal_type: SignalType::Stop,
        };

        if let Err(e) = self.broadcaster.broadcast_signal(stop_signal).await {
            self.messages
                .push(format!("⚠️ Failed to send stop signal: {}", e));
        } else {
            self.messages
                .push("📡 Stop signal sent to all clients".to_string());
        }

        // 2. 等待一小段时间让客户端处理Stop信号
        tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;

        // 3. 断开所有客户端连接
        let disconnected_count = self.broadcaster.disconnect_all_clients().await;

        // 4. 清除客户端管理器中的所有状态（包括持久化状态）
        {
            let mut manager = self.client_manager.lock().await;
            if let Err(e) = manager.clear_all_states() {
                self.messages
                    .push(format!("❌ Error clearing states: {}", e));
                return;
            }
        }

        self.messages.push(format!(
            "✅ Sent stop signal, disconnected {} clients and cleared all states.",
            disconnected_count
        ));
    }

    fn parse_signal_command(&self, input: &str) -> Option<SignalMessage> {
        let input = input.trim();

        // 处理简化命令
        match input.to_uppercase().as_str() {
            "OL" => Some(SignalMessage {
                signal_type: SignalType::DirectOpenLong,
            }),
            "OS" => Some(SignalMessage {
                signal_type: SignalType::DirectOpenShort,
            }),
            "C" => Some(SignalMessage {
                signal_type: SignalType::DirectClose,
            }),
            "STOP" => Some(SignalMessage {
                signal_type: SignalType::Stop,
            }),
            "R" => Some(SignalMessage {
                signal_type: SignalType::Reverse,
            }),
            _ => {
                // 处理复合命令
                let parts: Vec<&str> = input.split_whitespace().collect();

                match parts.as_slice() {
                    // DelayOpenLong命令: DOL <seconds>
                    [cmd, seconds_str] if cmd.to_uppercase() == "DOL" => {
                        if let Ok(delay_seconds) = seconds_str.parse::<u64>() {
                            // 计算绝对时间戳（毫秒）
                            let now = SystemTime::now();
                            let target_time = now + Duration::from_secs(delay_seconds);
                            let timestamp_ms = target_time
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_millis() as u64;

                            Some(SignalMessage {
                                signal_type: SignalType::DelayOpenLong(timestamp_ms),
                            })
                        } else {
                            None
                        }
                    }
                    // DelayOpenShort命令: DOS <seconds>
                    [cmd, seconds_str] if cmd.to_uppercase() == "DOS" => {
                        if let Ok(delay_seconds) = seconds_str.parse::<u64>() {
                            // 计算绝对时间戳（毫秒）
                            let now = SystemTime::now();
                            let target_time = now + Duration::from_secs(delay_seconds);
                            let timestamp_ms = target_time
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_millis() as u64;

                            Some(SignalMessage {
                                signal_type: SignalType::DelayOpenShort(timestamp_ms),
                            })
                        } else {
                            None
                        }
                    }
                    // DelayClose命令: DC <seconds>
                    [cmd, seconds_str] if cmd.to_uppercase() == "DC" => {
                        if let Ok(delay_seconds) = seconds_str.parse::<u64>() {
                            // 计算绝对时间戳（毫秒）
                            let now = SystemTime::now();
                            let target_time = now + Duration::from_secs(delay_seconds);
                            let timestamp_ms = target_time
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_millis() as u64;

                            Some(SignalMessage {
                                signal_type: SignalType::DelayClose(timestamp_ms),
                            })
                        } else {
                            None
                        }
                    }
                    // ChangeSymbol命令: S <symbol>
                    [cmd, symbol] if cmd.to_uppercase() == "S" => Some(SignalMessage {
                        signal_type: SignalType::ChangeSymbol(symbol.to_string()),
                    }),
                    // 完整命令格式
                    ["delay_open_long", seconds_str] => {
                        if let Ok(delay_seconds) = seconds_str.parse::<u64>() {
                            // 计算绝对时间戳（毫秒）
                            let now = SystemTime::now();
                            let target_time = now + Duration::from_secs(delay_seconds);
                            let timestamp_ms = target_time
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_millis() as u64;

                            Some(SignalMessage {
                                signal_type: SignalType::DelayOpenLong(timestamp_ms),
                            })
                        } else {
                            None
                        }
                    }
                    ["delay_open_short", seconds_str] => {
                        if let Ok(delay_seconds) = seconds_str.parse::<u64>() {
                            // 计算绝对时间戳（毫秒）
                            let now = SystemTime::now();
                            let target_time = now + Duration::from_secs(delay_seconds);
                            let timestamp_ms = target_time
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_millis() as u64;

                            Some(SignalMessage {
                                signal_type: SignalType::DelayOpenShort(timestamp_ms),
                            })
                        } else {
                            None
                        }
                    }
                    ["direct_open_long"] => Some(SignalMessage {
                        signal_type: SignalType::DirectOpenLong,
                    }),
                    ["direct_open_short"] => Some(SignalMessage {
                        signal_type: SignalType::DirectOpenShort,
                    }),
                    ["delay_close", seconds_str] => {
                        if let Ok(delay_seconds) = seconds_str.parse::<u64>() {
                            // 计算绝对时间戳（毫秒）
                            let now = SystemTime::now();
                            let target_time = now + Duration::from_secs(delay_seconds);
                            let timestamp_ms = target_time
                                .duration_since(std::time::UNIX_EPOCH)
                                .unwrap_or_default()
                                .as_millis() as u64;

                            Some(SignalMessage {
                                signal_type: SignalType::DelayClose(timestamp_ms),
                            })
                        } else {
                            None
                        }
                    }
                    ["direct_close"] => Some(SignalMessage {
                        signal_type: SignalType::DirectClose,
                    }),
                    ["stop"] => Some(SignalMessage {
                        signal_type: SignalType::Stop,
                    }),
                    ["reverse"] => Some(SignalMessage {
                        signal_type: SignalType::Reverse,
                    }),
                    // ChangeSymbol命令: change_symbol <symbol>
                    ["change_symbol", symbol] => Some(SignalMessage {
                        signal_type: SignalType::ChangeSymbol(symbol.to_string()),
                    }),
                    _ => None,
                }
            }
        }
    }

    fn update_positions(&mut self) {
        // Simulate price updates
        for position in self.positions.values_mut() {
            // Random price movement ±2%
            let change = (rand::random::<f64>() - 0.5) * 0.04;
            position.current_price *= 1.0 + change;

            // Recalculate PnL and percentage
            position.pnl = (position.current_price - position.entry_price) * position.size;
            position.percentage = if position.entry_price != 0.0 {
                ((position.current_price - position.entry_price) / position.entry_price) * 100.0
            } else {
                0.0
            };
        }
    }
}

/// 格式化时间为"多久之前"的形式
fn format_time_ago(time: SystemTime) -> String {
    match time.elapsed() {
        Ok(duration) => {
            let secs = duration.as_secs();
            if secs < 60 {
                "刚刚".to_string()
            } else if secs < 3600 {
                format!("{}分钟前", secs / 60)
            } else if secs < 86400 {
                format!("{}小时前", secs / 3600)
            } else {
                format!("{}天前", secs / 86400)
            }
        }
        Err(_) => "未知".to_string(),
    }
}
