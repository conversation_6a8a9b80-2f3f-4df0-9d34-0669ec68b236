use crate::message::{ReportMessage, ServerInfo};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::net::SocketAddr;
use std::path::Path;
use std::time::SystemTime;
use tracing::{info, warn};

/// 客户端连接信息
#[derive(Debug, Clone)]
pub struct ClientInfo {
    pub id: String,
    #[allow(dead_code)]
    pub addr: SocketAddr,
    pub connected_at: SystemTime,
    pub server_info: Option<ServerInfo>,
    pub last_report: Option<ReportMessage>,
    pub reconnect_count: u32,
    pub last_seen: SystemTime,
}

/// 持久化的客户端状态（基于server_name）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PersistentClientState {
    pub server_name: String,
    pub exchange_name: String,
    // 分别存储不同类型的报告状态
    pub available_long: Option<f64>,
    pub available_short: Option<f64>,
    pub current_long: Option<f64>,
    pub current_short: Option<f64>,
    pub balance: Option<f64>,
    pub unrealized_pnl: Option<f64>,
    pub total_connections: u32,
    #[serde(with = "systemtime_serde")]
    pub first_seen: SystemTime,
    #[serde(with = "systemtime_serde")]
    pub last_seen: SystemTime,
    #[serde(skip)] // 当前会话ID不需要持久化
    pub current_session_id: Option<String>,
}

/// SystemTime的序列化/反序列化辅助模块
mod systemtime_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use std::time::{SystemTime, UNIX_EPOCH};

    pub fn serialize<S>(time: &SystemTime, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        let duration = time.duration_since(UNIX_EPOCH).unwrap();
        duration.as_secs().serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<SystemTime, D::Error>
    where
        D: Deserializer<'de>,
    {
        let secs = u64::deserialize(deserializer)?;
        Ok(UNIX_EPOCH + std::time::Duration::from_secs(secs))
    }
}

#[derive(Debug)]
pub struct ClientManager {
    /// 当前活跃的客户端连接 (client_id -> ClientInfo)
    clients: HashMap<String, ClientInfo>,
    /// 持久化的客户端状态 (server_name -> PersistentClientState)
    persistent_states: HashMap<String, PersistentClientState>,
}

impl ClientManager {
    const STATE_FILE: &'static str = "data/client_states.json";

    pub fn new() -> Self {
        let mut manager = Self {
            clients: HashMap::new(),
            persistent_states: HashMap::new(),
        };

        // 尝试从文件加载持久化状态
        if let Err(e) = manager.load_persistent_states() {
            warn!("Failed to load persistent states: {}", e);
        }

        manager
    }

    /// 从文件加载持久化状态
    fn load_persistent_states(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if !Path::new(Self::STATE_FILE).exists() {
            info!("No persistent state file found, starting fresh");
            return Ok(());
        }

        let content = std::fs::read_to_string(Self::STATE_FILE)?;
        let states: HashMap<String, PersistentClientState> = serde_json::from_str(&content)?;

        self.persistent_states = states;

        info!(
            "Loaded {} persistent client states from file",
            self.persistent_states.len()
        );

        // 记录加载的状态
        for (server_name, state) in &self.persistent_states {
            info!(
                server_name = %server_name,
                exchange = %state.exchange_name,
                total_connections = state.total_connections,
                last_seen = ?state.last_seen,
                "Restored client state"
            );
        }

        Ok(())
    }

    /// 保存持久化状态到文件
    fn save_persistent_states(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 确保数据目录存在
        if let Some(parent) = Path::new(Self::STATE_FILE).parent() {
            std::fs::create_dir_all(parent)?;
        }

        let content = serde_json::to_string_pretty(&self.persistent_states)?;
        std::fs::write(Self::STATE_FILE, content)?;

        info!(
            "Saved {} persistent client states to file",
            self.persistent_states.len()
        );
        Ok(())
    }

    /// 添加新客户端，如果是重连则恢复状态
    pub fn add_client(&mut self, mut client: ClientInfo) -> bool {
        let mut is_reconnect = false;

        // 如果客户端有server_info，检查是否是重连
        if let Some(ref server_info) = client.server_info {
            if let Some(persistent_state) = self.persistent_states.get_mut(&server_info.server_name)
            {
                // 这是一个重连的客户端
                is_reconnect = true;
                client.reconnect_count = persistent_state.total_connections;
                // 客户端的last_report会在收到新报告时更新

                // 更新持久化状态
                persistent_state.total_connections += 1;
                persistent_state.last_seen = client.connected_at;
                persistent_state.current_session_id = Some(client.id.clone());

                tracing::info!(
                    client_id = %client.id,
                    server_name = %server_info.server_name,
                    reconnect_count = persistent_state.total_connections,
                    "客户端重连，恢复状态"
                );
            } else {
                // 新客户端，创建持久化状态
                let persistent_state = PersistentClientState {
                    server_name: server_info.server_name.clone(),
                    exchange_name: server_info.exchange_name.clone(),
                    available_long: None,
                    available_short: None,
                    current_long: None,
                    current_short: None,
                    balance: None,
                    unrealized_pnl: None,
                    total_connections: 1,
                    first_seen: client.connected_at,
                    last_seen: client.connected_at,
                    current_session_id: Some(client.id.clone()),
                };

                self.persistent_states
                    .insert(server_info.server_name.clone(), persistent_state);

                tracing::info!(
                    client_id = %client.id,
                    server_name = %server_info.server_name,
                    "新客户端首次连接"
                );
            }

            // 保存状态到文件
            if let Err(e) = self.save_persistent_states() {
                warn!("Failed to save persistent states: {}", e);
            }
        }

        self.clients.insert(client.id.clone(), client);
        is_reconnect
    }

    /// 移除客户端连接，但保持持久化状态
    pub fn remove_client(&mut self, client_id: &str) -> Option<ClientInfo> {
        if let Some(client) = self.clients.remove(client_id) {
            // 更新持久化状态中的当前会话ID
            if let Some(ref server_info) = client.server_info {
                if let Some(persistent_state) =
                    self.persistent_states.get_mut(&server_info.server_name)
                {
                    if persistent_state.current_session_id.as_deref() == Some(client_id) {
                        persistent_state.current_session_id = None;
                        persistent_state.last_seen = SystemTime::now();

                        tracing::info!(
                            client_id = %client_id,
                            server_name = %server_info.server_name,
                            "客户端断开连接，保持状态"
                        );
                    }
                }
            }

            Some(client)
        } else {
            None
        }
    }

    /// 更新客户端服务器信息，同时更新持久化状态
    pub fn update_server_info(&mut self, client_id: &str, server_info: ServerInfo) {
        if let Some(client) = self.clients.get_mut(client_id) {
            let old_server_name = client
                .server_info
                .as_ref()
                .map(|info| info.server_name.clone());
            client.server_info = Some(server_info.clone());
            client.last_seen = SystemTime::now();

            // 如果server_name发生变化，需要更新持久化状态
            if old_server_name.as_ref() != Some(&server_info.server_name) {
                // 从旧的持久化状态中移除
                if let Some(old_name) = old_server_name {
                    if let Some(old_state) = self.persistent_states.get_mut(&old_name) {
                        if old_state.current_session_id.as_deref() == Some(client_id) {
                            old_state.current_session_id = None;
                        }
                    }
                }

                // 添加到新的持久化状态或创建新状态
                if let Some(persistent_state) =
                    self.persistent_states.get_mut(&server_info.server_name)
                {
                    persistent_state.current_session_id = Some(client_id.to_string());
                    persistent_state.last_seen = SystemTime::now();
                    persistent_state.total_connections += 1;
                } else {
                    let persistent_state = PersistentClientState {
                        server_name: server_info.server_name.clone(),
                        exchange_name: server_info.exchange_name.clone(),
                        available_long: None,
                        available_short: None,
                        current_long: None,
                        current_short: None,
                        balance: None,
                        unrealized_pnl: None,
                        total_connections: 1,
                        first_seen: client.connected_at,
                        last_seen: SystemTime::now(),
                        current_session_id: Some(client_id.to_string()),
                    };
                    self.persistent_states
                        .insert(server_info.server_name.clone(), persistent_state);
                }
            }

            // 保存状态到文件
            if let Err(e) = self.save_persistent_states() {
                warn!("Failed to save persistent states: {}", e);
            }
        }
    }

    /// 更新客户端报告信息，同时更新持久化状态
    pub fn update_report(&mut self, client_id: &str, report: ReportMessage) {
        if let Some(client) = self.clients.get_mut(client_id) {
            client.last_report = Some(report.clone());
            client.last_seen = SystemTime::now();

            // 同时更新持久化状态
            if let Some(ref server_info) = client.server_info {
                if let Some(persistent_state) =
                    self.persistent_states.get_mut(&server_info.server_name)
                {
                    // 根据报告类型更新相应的字段
                    match report {
                        ReportMessage::AvailableLong(amount) => {
                            persistent_state.available_long = Some(amount);
                        }
                        ReportMessage::AvailableShort(amount) => {
                            persistent_state.available_short = Some(amount);
                        }
                        ReportMessage::CurrentLong(amount) => {
                            persistent_state.current_long = Some(amount);
                        }
                        ReportMessage::CurrentShort(amount) => {
                            persistent_state.current_short = Some(amount);
                        }
                        ReportMessage::Balance(amount) => {
                            persistent_state.balance = Some(amount);
                        }
                        ReportMessage::UnrealizedPnl(amount) => {
                            persistent_state.unrealized_pnl = Some(amount);
                        }
                        _ => {} // 其他类型的报告暂时不处理
                    }

                    persistent_state.last_seen = SystemTime::now();

                    // 保存状态到文件
                    if let Err(e) = self.save_persistent_states() {
                        warn!("Failed to save persistent states: {}", e);
                    }
                }
            }
        }
    }

    /// 获取所有持久化状态
    #[allow(dead_code)]
    pub fn get_persistent_states(&self) -> &HashMap<String, PersistentClientState> {
        &self.persistent_states
    }

    /// 获取特定服务器的持久化状态
    #[allow(dead_code)]
    pub fn get_persistent_state(&self, server_name: &str) -> Option<&PersistentClientState> {
        self.persistent_states.get(server_name)
    }

    /// 获取按交易所聚合的状态信息
    #[allow(dead_code)]
    pub fn get_exchange_summary(&self) -> HashMap<String, ExchangeSummary> {
        let mut summary = HashMap::new();

        for state in self.persistent_states.values() {
            let entry = summary
                .entry(state.exchange_name.clone())
                .or_insert_with(|| ExchangeSummary {
                    exchange_name: state.exchange_name.clone(),
                    total_clients: 0,
                    online_clients: 0,
                    available_long: 0.0,
                    available_short: 0.0,
                    current_long: 0.0,
                    current_short: 0.0,
                    balance: 0.0,
                    unrealized_pnl: 0.0,
                    last_update: state.last_seen,
                });

            entry.total_clients += 1;

            // 检查是否在线
            if state.current_session_id.is_some() {
                entry.online_clients += 1;
            }

            // 聚合报告信息
            if let Some(amount) = state.available_long {
                entry.available_long += amount;
            }
            if let Some(amount) = state.available_short {
                entry.available_short += amount;
            }
            if let Some(amount) = state.current_long {
                entry.current_long += amount;
            }
            if let Some(amount) = state.current_short {
                entry.current_short += amount;
            }
            if let Some(amount) = state.balance {
                entry.balance += amount;
            }
            if let Some(amount) = state.unrealized_pnl {
                entry.unrealized_pnl += amount;
            }

            // 更新最后更新时间
            if state.last_seen > entry.last_update {
                entry.last_update = state.last_seen;
            }
        }

        summary
    }

    /// 清理长时间未连接的持久化状态（可选功能）
    #[allow(dead_code)]
    pub fn cleanup_old_states(&mut self, max_age_hours: u64) {
        let cutoff_time = SystemTime::now() - std::time::Duration::from_secs(max_age_hours * 3600);

        self.persistent_states.retain(|server_name, state| {
            let should_keep = state.last_seen > cutoff_time;
            if !should_keep {
                tracing::info!(
                    server_name = %server_name,
                    last_seen = ?state.last_seen,
                    "清理长时间未连接的客户端状态"
                );
            }
            should_keep
        });
    }

    /// 清除所有客户端状态
    pub fn clear_all_states(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        let client_count = self.clients.len();
        let persistent_count = self.persistent_states.len();

        // 清除活跃连接
        self.clients.clear();

        // 清除持久化状态
        self.persistent_states.clear();

        // 保存空状态到文件
        self.save_persistent_states()?;

        info!(
            cleared_active_clients = client_count,
            cleared_persistent_states = persistent_count,
            "Cleared all client states"
        );

        Ok(())
    }

    /// 只清除活跃连接，保留持久化状态
    #[allow(dead_code)]
    pub fn clear_active_connections(&mut self) {
        let client_count = self.clients.len();

        // 清除活跃连接
        self.clients.clear();

        // 将所有持久化状态标记为离线（清除session_id）
        for state in self.persistent_states.values_mut() {
            state.current_session_id = None;
        }

        info!(
            cleared_active_clients = client_count,
            preserved_persistent_states = self.persistent_states.len(),
            "Cleared active connections, preserved historical states"
        );
    }

    /// 检查是否所有客户端都在线
    pub fn all_clients_online(&self) -> bool {
        if self.persistent_states.is_empty() {
            return true; // 没有客户端时认为状态正常
        }

        self.persistent_states
            .values()
            .all(|state| state.current_session_id.is_some())
    }

    /// 获取离线客户端列表
    pub fn get_offline_clients(&self) -> Vec<String> {
        self.persistent_states
            .values()
            .filter(|state| state.current_session_id.is_none())
            .map(|state| state.server_name.clone())
            .collect()
    }
}

/// 交易所汇总信息
#[derive(Debug, Clone)]
#[allow(dead_code)]
pub struct ExchangeSummary {
    pub exchange_name: String,
    pub total_clients: u32,
    pub online_clients: u32,
    pub available_long: f64,
    pub available_short: f64,
    pub current_long: f64,
    pub current_short: f64,
    pub balance: f64,
    pub unrealized_pnl: f64,
    pub last_update: SystemTime,
}

impl Default for ClientManager {
    fn default() -> Self {
        Self::new()
    }
}
