use anyhow::Result;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::info;
use tracing_appender::rolling::{RollingFileAppender, Rotation};
use tracing_subscriber::{fmt, layer::SubscriberExt, util::SubscriberInitExt};

mod client_manager;
mod message;
mod tui;
mod websocket;

use client_manager::ClientManager;
use tui::TuiApp;
use websocket::WebSocketServer;

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统 - 输出到文件
    setup_logging()?;

    start_server().await
}

fn setup_logging() -> Result<()> {
    // 创建日志目录
    std::fs::create_dir_all("logs")?;

    // 创建滚动文件appender - 每天一个新文件
    let file_appender = RollingFileAppender::new(Rotation::DAILY, "logs", "control-panel.log");

    // 配置日志格式
    let file_layer = fmt::layer()
        .with_writer(file_appender)
        .with_ansi(false) // 文件中不需要颜色代码
        .with_target(true)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true);

    // 初始化订阅者
    tracing_subscriber::registry().with(file_layer).init();

    info!("日志系统初始化完成 - 日志文件: logs/control-panel.log");

    Ok(())
}

async fn start_server() -> Result<()> {
    let host = "0.0.0.0";
    let port = 8181;

    info!("Starting WebSocket server on {}:{}", host, port);

    // Create shared client manager
    let client_manager = Arc::new(Mutex::new(ClientManager::new()));

    // Start WebSocket server
    let (ws_server, broadcaster) = WebSocketServer::new(client_manager.clone());
    let server_handle = tokio::spawn(async move {
        if let Err(e) = ws_server.start(host, port).await {
            eprintln!("WebSocket server error: {}", e);
        }
    });

    // Start TUI
    let mut tui_app = TuiApp::new(client_manager.clone(), broadcaster);
    let tui_result = tui_app.run().await;

    // Clean shutdown
    server_handle.abort();

    tui_result
}
