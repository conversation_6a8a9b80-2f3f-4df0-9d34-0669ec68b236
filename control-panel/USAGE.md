# Control Panel 使用指南

## 基本用法

### 1. 启动服务器

```bash
# 直接启动 (默认监听 127.0.0.1:8080)
cargo run --bin control-panel
```

### 2. TUI 界面操作

启动后，你将看到一个 TUI 界面，包含三个标签页：Clients、Positions 和 Messages。

- 使用 `Tab` 键切换标签页
- 使用 `i` 键进入输入模式
- 在输入模式下，输入消息后按 `Enter` 发送给所有连接的客户端
- 使用 `Esc` 键退出输入模式
- 使用 `q` 键退出程序

### 3. 发送消息给客户端

1. 按 `i` 键进入输入模式（输入框会变成黄色）
2. 输入你想发送的消息
3. 按 `Enter` 键发送消息给所有连接的客户端
4. 消息会显示在 Messages 标签页中

### 3. 测试客户端

在另一个终端中运行测试客户端：

```bash
cargo run --bin test_client
```

## 持仓显示示例

在 Positions 标签页中，你可以看到类似下面的持仓信息：

```
┌Positions────────────────────────────────────────────────────────────────┐
│Symbol    Size      Entry     Current   PnL         Percentage           │
│BTC/USDT  0.5000    45000.00  47000.00  1000.00     4.44%               │
│ETH/USDT  2.0000    3000.00   3200.00   400.00      6.67%               │
└──────────────────────────────────────────────────────────────────────────┘
```

## 客户端 WebSocket API

### 连接

```javascript
// JavaScript 示例
const ws = new WebSocket('ws://127.0.0.1:8080');

ws.onopen = () => {
  console.log('Connected to server');
};

ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  console.log('Received:', message);
};
```

### 消息格式

服务器发送的消息格式：

1. 欢迎消息

```json
{
  "type": "welcome",
  "client_id": "4cdecf50-7594-461c-acca-06a94efa25e7",
  "server_time": "2023-01-01T00:00:00Z"
}
```

2. 持仓更新

```json
{
  "type": "position_update",
  "positions": {
    "BTC/USDT": {
      "symbol": "BTC/USDT",
      "size": 0.5,
      "entry_price": 45000.0,
      "current_price": 47000.0,
      "pnl": 1000.0,
      "percentage": 4.44
    },
    "ETH/USDT": {
      "symbol": "ETH/USDT",
      "size": 2.0,
      "entry_price": 3000.0,
      "current_price": 3200.0,
      "pnl": 400.0,
      "percentage": 6.67
    }
  },
  "timestamp": "2023-01-01T00:00:00Z"
}
```

3. 广播消息

```json
{
  "type": "broadcast",
  "message": "Important announcement from server",
  "timestamp": "2023-01-01T00:00:00Z",
  "from_server": true
}
```

## 扩展功能

### 添加新的持仓数据

修改 `tui.rs` 中的 `TuiApp::new()` 方法：

```rust
// 添加更多持仓
positions.insert(
    "SOL/USDT".to_string(),
    PositionInfo::new("SOL/USDT".to_string(), 10.0, 100.0, 120.0),
);
```

### 实现真实的客户端管理

目前客户端管理功能已经实现，但 TUI 界面没有显示连接的客户端。要修复这个问题，修改 `tui.rs` 中的 `render_clients_tab` 方法：

```rust
async fn render_clients_tab(&self, f: &mut Frame, area: Rect) {
    let manager = self.client_manager.lock().await;
    let clients = manager.get_all_clients();

    let items: Vec<ListItem> = if clients.is_empty() {
        vec![
            ListItem::new(format!("Connected Clients: 0")),
            ListItem::new("No clients connected"),
        ]
    } else {
        let mut items = vec![ListItem::new(format!("Connected Clients: {}", clients.len()))];
        for client in clients {
            items.push(ListItem::new(format!("ID: {}", client.id)));
        }
        items
    };

    let clients_list = List::new(items)
        .block(Block::default().borders(Borders::ALL).title("Connected Clients"))
        .style(Style::default().fg(Color::White));

    f.render_widget(clients_list, area);
}
```

## 故障排除

### 端口已被占用

如果启动服务器时出现 "Address already in use" 错误，可以尝试使用不同的端口：

```bash
cargo run --bin control-panel -- start --port 9090
```

### 客户端连接失败

确保服务器正在运行，并且客户端连接的地址和端口正确。如果服务器绑定在 `127.0.0.1`，则只能从本机访问。如果需要从其他机器访问，请使用：

```bash
cargo run --bin control-panel -- start --host 0.0.0.0
```
