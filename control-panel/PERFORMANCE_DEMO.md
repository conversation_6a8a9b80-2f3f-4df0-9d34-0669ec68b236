# 高性能WebSocket控制面板演示

## 功能特性

### 🚀 高性能架构
- **异步处理**：使用Tokio异步运行时，支持数千个并发连接
- **零拷贝消息传递**：使用mpsc通道进行高效的消息传递
- **广播优化**：使用broadcast通道实现同时向所有客户端发送信号
- **连接池管理**：使用RwLock实现高效的连接管理
- **状态持久化**：基于server_name的客户端状态保持，支持断线重连

### 📊 详细日志记录
- **结构化日志**：使用tracing框架提供详细的结构化日志
- **性能指标**：实时监控连接数、消息发送/接收统计
- **链路追踪**：每个客户端连接都有独立的trace span
- **错误处理**：详细的错误日志和恢复机制

### 💬 消息协议
支持以下客户端消息类型：
1. **ServerInfo** - 客户端上报服务器信息
2. **Report** - 客户端上报仓位信息
   - AvailableOpen(f64) - 可开仓位
   - CurrentPosition(f64) - 当前仓位
   - StartOpen(u64) - 开始建仓时间戳
   - StartClose(u64) - 开始平仓时间戳

支持以下信号类型：
1. **DelayOpen(u64)** - 延迟开仓信号
2. **DirectOpen** - 立即开仓信号
3. **DelayClose(u64)** - 延迟平仓信号
4. **DirectClose** - 立即平仓信号

## 使用方法

### 1. 启动控制面板
```bash
cargo run --bin control-panel
```

### 2. 启动测试客户端
```bash
# 基础测试客户端
cargo run --bin test_client

# 重连测试客户端（演示状态保持）
cargo run --bin reconnect_test_client "My Trading Bot" "Binance"
```

### 3. 重连功能演示
```bash
# 运行完整的重连演示
./reconnect_demo.sh
```

### 3. 发送信号
在控制面板TUI中：
1. 按 `i` 进入输入模式
2. 输入信号命令：
   - `direct_open` - 立即开仓
   - `delay_open 5` - 5秒后开仓
   - `direct_close` - 立即平仓
   - `delay_close 10` - 10秒后平仓
3. 按 `Enter` 发送信号
4. 按 `Esc` 退出输入模式
5. 按 `Tab` 切换标签页
6. 按 `q` 退出程序

### 4. 查看日志
服务器会输出详细的结构化日志，包括：
- 客户端连接/断开事件
- 消息接收/发送统计
- 信号广播事件
- 性能指标（每30秒）

## 性能特点

### 并发处理
- 每个客户端连接运行在独立的异步任务中
- 消息发送和接收分离，避免阻塞
- 使用无锁数据结构优化性能

### 内存效率
- 零拷贝消息传递
- 连接池复用
- 智能缓冲区管理

### 网络优化
- WebSocket协议优化
- 心跳检测
- 自动重连机制

### 监控指标
- 实时连接数统计
- 消息吞吐量监控
- 延迟统计
- 错误率监控

## 🔄 客户端重连状态保持

### 核心特性
- **基于server_name的状态识别**：使用客户端的server_name作为唯一标识
- **自动状态恢复**：重连时自动恢复最后的报告状态和连接历史
- **连接计数追踪**：记录每个客户端的总连接次数
- **时间戳管理**：跟踪首次连接时间和最后活跃时间

### 工作原理
1. **首次连接**：客户端发送ServerInfo，系统创建持久化状态
2. **状态更新**：客户端的报告信息同时更新到持久化状态
3. **断开连接**：连接断开时保持持久化状态，只清除当前会话ID
4. **重新连接**：相同server_name的客户端重连时自动恢复状态

### 状态包含信息
- 服务器名称和交易所
- 最后的报告信息（仓位、可用资金等）
- 总连接次数和重连计数
- 首次连接和最后活跃时间
- 当前会话ID（如果在线）

## 扩展性

该架构可以轻松扩展到：
- 支持更多消息类型
- 添加认证和授权
- 集群部署
- 数据持久化到数据库
- 更复杂的路由规则
- 客户端状态的长期存储

## 日志示例

```
2025-08-02T06:20:57.592705Z  INFO handle_connection{client_addr=127.0.0.1:44154}:client_session{client_id=6f4ba15b-07df-48ae-893e-97f79a3f794d}: control_panel::websocket: Client connected successfully

2025-08-02T06:20:57.592902Z  INFO handle_connection{client_addr=127.0.0.1:44154}:client_session{client_id=6f4ba15b-07df-48ae-893e-97f79a3f794d}:message_handler_loop{client_id=6f4ba15b-07df-48ae-893e-97f79a3f794d}:handle_client_message{client_id=6f4ba15b-07df-48ae-893e-97f79a3f794d}: control_panel::websocket: Received server info from client server_name=Test Trading Bot exchange_name=Binance timestamp=1754115657

2025-08-02T06:21:06.407530Z  INFO handle_connection{client_addr=127.0.0.1:44154}:client_session{client_id=6f4ba15b-07df-48ae-893e-97f79a3f794d}: control_panel::websocket: Server performance metrics active_connections=1 total_connections=1 messages_sent=4 messages_received=3 signals_broadcast=0
```
