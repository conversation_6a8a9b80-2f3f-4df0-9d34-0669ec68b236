# Control Panel 演示指南

## 功能演示

这个 WebSocket Control Panel 已经实现了你要求的核心功能：

### ✅ 已实现的功能

1. **直接启动** - 无需命令行参数，直接运行即可
2. **WebSocket 服务器** - 自动监听 127.0.0.1:8080
3. **TUI 界面** - 现代化的终端用户界面
4. **消息广播** - 通过 TUI 输入框向所有连接的客户端发送消息
5. **持仓显示** - 实时显示持仓信息（模拟数据）
6. **客户端管理** - 后台管理连接的客户端

### 🚀 快速演示

#### 步骤 1: 启动 Control Panel

```bash
cd control-panel
cargo run --bin control-panel
```

启动后你会看到 TUI 界面，包含三个标签页：
- **Clients**: 客户端连接信息
- **Positions**: 持仓信息（实时更新）
- **Messages**: 消息日志

#### 步骤 2: 连接测试客户端

在另一个终端中运行：

```bash
cargo run --bin test_client
```

你会看到：
- 客户端成功连接到服务器
- 收到欢迎消息
- 发送测试消息并收到回显

#### 步骤 3: 通过 TUI 发送消息

在 Control Panel 的 TUI 界面中：

1. 按 `i` 键进入输入模式（输入框变黄色）
2. 输入消息，例如："Hello from server!"
3. 按 `Enter` 发送消息
4. 切换到 Messages 标签页查看发送记录
5. 客户端会收到广播消息

#### 步骤 4: 查看持仓信息

1. 按 `Tab` 键切换到 Positions 标签页
2. 查看实时更新的持仓信息：
   - BTC/USDT: 0.5000 @ 45000.00 → 当前价格（实时变化）
   - ETH/USDT: 2.0000 @ 3000.00 → 当前价格（实时变化）
3. PnL 和百分比会根据价格变化实时计算

### 🎮 TUI 操作指南

| 按键 | 功能 |
|------|------|
| `Tab` | 切换标签页 |
| `i` | 进入输入模式 |
| `Enter` | 发送消息（输入模式下） |
| `Esc` | 退出输入模式 |
| `q` | 退出程序 |

### 📊 消息格式

#### 服务器发送的消息类型：

1. **欢迎消息**
```json
{
  "type": "welcome",
  "client_id": "uuid",
  "server_time": "2023-01-01T00:00:00Z"
}
```

2. **广播消息**
```json
{
  "type": "broadcast",
  "message": "Hello from server!",
  "timestamp": "2023-01-01T00:00:00Z",
  "from_server": true
}
```

3. **回显消息**
```json
{
  "type": "echo",
  "original_message": "Hello from client!",
  "timestamp": "2023-01-01T00:00:00Z"
}
```

### 🔧 架构说明

- **main.rs**: 程序入口，启动 WebSocket 服务器和 TUI
- **websocket.rs**: WebSocket 服务器实现，支持消息广播
- **tui.rs**: TUI 界面实现，处理用户输入和显示
- **client_manager.rs**: 客户端连接管理
- **message.rs**: 消息类型定义

### 🎯 核心特性

1. **无参数启动**: 直接运行 `cargo run --bin control-panel`
2. **消息广播**: 在 TUI 输入框中输入消息，按 Enter 发送给所有客户端
3. **实时持仓**: 持仓数据每 5 秒自动更新价格
4. **异步处理**: 使用 Tokio 实现高性能异步处理
5. **类型安全**: 使用 serde 进行消息序列化

### 📝 使用场景

这个框架适用于：
- 交易所控制面板
- 实时数据推送系统
- 客户端状态监控
- 消息广播系统
- 持仓管理界面

### 🚀 扩展建议

1. **连接真实交易所 API** 获取实时价格数据
2. **实现用户认证** 添加客户端认证机制
3. **数据持久化** 保存历史数据和配置
4. **更多消息类型** 支持订单、交易等消息
5. **客户端实时显示** 在 Clients 标签页显示实时连接信息

这个框架已经为你提供了一个完整的基础，可以根据具体需求进行扩展！
