#!/bin/bash

# 高性能WebSocket控制面板演示脚本

echo "🚀 启动高性能WebSocket控制面板演示"
echo "=================================="

# 检查是否已经有服务器在运行
if lsof -i :8080 > /dev/null 2>&1; then
    echo "⚠️  端口8080已被占用，请先停止现有服务器"
    exit 1
fi

# 启动控制面板服务器
echo "📡 启动控制面板服务器..."
cargo run --bin control-panel &
SERVER_PID=$!

# 等待服务器启动
sleep 3

echo "🔗 启动多个测试客户端..."

# 启动5个测试客户端来模拟高并发
for i in {1..5}; do
    echo "启动客户端 #$i"
    cargo run --bin test_client &
    CLIENT_PIDS+=($!)
    sleep 0.5
done

echo ""
echo "✅ 演示环境已启动！"
echo ""
echo "📋 使用说明："
echo "1. 控制面板TUI已启动，可以看到连接的客户端"
echo "2. 按 'i' 进入输入模式"
echo "3. 输入信号命令测试广播："
echo "   - direct_open (立即开仓)"
echo "   - delay_open 5 (5秒后开仓)"
echo "   - direct_close (立即平仓)"
echo "   - delay_close 10 (10秒后平仓)"
echo "4. 按 Tab 切换标签页查看不同信息"
echo "5. 按 'q' 退出程序"
echo ""
echo "📊 性能监控："
echo "- 服务器每30秒输出性能指标到日志文件"
echo "- 观察日志中的连接数、消息统计等"
echo "- 所有客户端将同时接收到信号"
echo ""
echo "📁 查看日志："
echo "在另一个终端运行: tail -f logs/control-panel.log"
echo "或者在TUI中切换到'日志状态'标签页"
echo ""
echo "🛑 停止演示："
echo "按 Ctrl+C 停止所有进程"

# 等待用户中断
trap 'echo ""; echo "🛑 停止演示..."; kill $SERVER_PID 2>/dev/null; for pid in "${CLIENT_PIDS[@]}"; do kill $pid 2>/dev/null; done; echo "✅ 演示已停止"; exit 0' INT

# 保持脚本运行
wait $SERVER_PID
