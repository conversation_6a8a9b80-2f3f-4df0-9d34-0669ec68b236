# Control Panel - WebSocket CLI Tool with TUI

这是一个基于 Rust 的 WebSocket 服务器 CLI 工具，具有终端用户界面 (TUI)，可以管理客户端连接并显示持仓信息。

## 功能特性

- **WebSocket 服务器**: 支持多客户端连接
- **TUI 界面**: 基于 ratatui 的现代终端界面
- **实时持仓显示**: 显示持仓比例、PnL 等信息
- **客户端管理**: 查看连接的客户端
- **消息推送**: 向客户端发送消息

## 安装和运行

### 构建项目

```bash
cd control-panel
cargo build --release
```

### 启动服务器

```bash
# 直接启动 (默认监听 127.0.0.1:8080)
cargo run --bin control-panel
```

### 测试客户端

在另一个终端中运行测试客户端：

```bash
cargo run --bin test_client
```

## TUI 界面操作

### 键盘快捷键

- `Tab`: 切换标签页 (Clients/Positions/Messages)
- `i`: 进入输入模式
- `Esc`: 退出输入模式
- `Enter`: 发送消息 (在输入模式下)
- `q`: 退出程序

### 标签页说明

1. **Clients**: 显示连接的客户端信息
2. **Positions**: 显示持仓信息，包括：
   - 交易对 (Symbol)
   - 持仓大小 (Size)
   - 入场价格 (Entry)
   - 当前价格 (Current)
   - 盈亏 (PnL)
   - 盈亏百分比 (Percentage)
3. **Messages**: 显示消息日志

## 架构说明

### 模块结构

- `main.rs`: 主程序入口和 CLI 参数处理
- `websocket.rs`: WebSocket 服务器实现
- `tui.rs`: TUI 界面实现
- `client_manager.rs`: 客户端连接管理
- `message.rs`: 消息类型定义

### 消息格式

服务器支持以下消息类型：

```json
{
  "type": "welcome",
  "client_id": "uuid",
  "server_time": "2023-01-01T00:00:00Z"
}

{
  "type": "position_update",
  "positions": {
    "BTC/USDT": {
      "symbol": "BTC/USDT",
      "size": 0.5,
      "entry_price": 45000.0,
      "current_price": 47000.0,
      "pnl": 1000.0,
      "percentage": 4.44
    }
  },
  "timestamp": "2023-01-01T00:00:00Z"
}
```

## 扩展功能

### 添加新的持仓数据

在 `tui.rs` 的 `TuiApp::new()` 方法中添加更多持仓：

```rust
positions.insert(
    "ETH/USDT".to_string(),
    PositionInfo::new("ETH/USDT".to_string(), 2.0, 3000.0, 3200.0),
);
```

### 实现真实的客户端管理

修改 `render_clients_tab` 方法来显示真实的客户端数据：

```rust
async fn render_clients_tab(&self, f: &mut Frame, area: Rect) {
    let manager = self.client_manager.lock().await;
    let clients = manager.get_all_clients();
    // 渲染客户端列表
}
```

### 添加更多消息类型

在 `message.rs` 中扩展 `ServerMessage` 和 `ClientMessage` 枚举。

## 依赖项

主要依赖项：

- `tokio`: 异步运行时
- `tokio-tungstenite`: WebSocket 实现
- `ratatui`: TUI 框架
- `crossterm`: 跨平台终端操作
- `serde`: 序列化/反序列化
- `clap`: CLI 参数解析

## 许可证

MIT License
