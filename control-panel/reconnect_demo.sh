#!/bin/bash

# 客户端重连状态保持演示脚本

echo "🔄 客户端重连状态保持演示"
echo "=========================="

# 检查是否已经有服务器在运行
if lsof -i :8080 > /dev/null 2>&1; then
    echo "⚠️  端口8080已被占用，请先停止现有服务器"
    exit 1
fi

# 启动控制面板服务器
echo "📡 启动控制面板服务器..."
cargo run --bin control-panel &
SERVER_PID=$!

# 等待服务器启动
sleep 3

echo ""
echo "🔄 演示客户端重连功能..."
echo ""

# 启动第一个持久化客户端
echo "🤖 启动第一个客户端: 'Persistent Bot A'"
cargo run --bin reconnect_test_client "Persistent Bot A" "Binance" &
CLIENT_A_PID=$!

sleep 2

# 启动第二个持久化客户端
echo "🤖 启动第二个客户端: 'Persistent Bot B'"
cargo run --bin reconnect_test_client "Persistent Bot B" "OKX" &
CLIENT_B_PID=$!

echo ""
echo "✅ 演示环境已启动！"
echo ""
echo "📋 重连功能演示说明："
echo "1. 两个客户端将自动进行多次重连"
echo "2. 每次重连时，服务器会恢复客户端的历史状态"
echo "3. 在TUI中可以看到："
echo "   - 活跃连接表格显示当前在线的客户端"
echo "   - 客户端状态历史表格显示所有客户端的重连记录"
echo "   - 重连次数会累计增加"
echo "   - 最后的报告状态会被保持"
echo ""
echo "🎯 测试重连状态保持："
echo "1. 观察TUI中的'客户端'标签页"
echo "2. 查看日志文件: tail -f logs/control-panel.log.*"
echo "3. 在TUI中发送信号测试广播功能"
echo ""
echo "📊 日志中会显示："
echo "- '新客户端首次连接' - 第一次连接"
echo "- '客户端重连，恢复状态' - 后续重连"
echo "- '客户端断开连接，保持状态' - 断开时保持状态"
echo ""
echo "🛑 停止演示："
echo "按 Ctrl+C 停止所有进程"

# 等待用户中断
trap 'echo ""; echo "🛑 停止演示..."; kill $SERVER_PID 2>/dev/null; kill $CLIENT_A_PID 2>/dev/null; kill $CLIENT_B_PID 2>/dev/null; echo "✅ 演示已停止"; exit 0' INT

# 保持脚本运行
wait $SERVER_PID
